/*---------------------------------------------------------------------------------------------
 * Copyright (c) Bentley Systems, Incorporated. All rights reserved.
 * See LICENSE.md in the repository root for full copyright notice.
 *--------------------------------------------------------------------------------------------*/

#pragma once

#include <TerrainModel/Core/DTMException.h>
#include <TerrainModel/Core/DTMFeature.h>
#include <Bentley/BeAssert.h>
#include <Bentley/bvector.h>
#include <Bentley/bmap.h>
#include <Bentley/bset.h>
#include <Bentley/WString.h>
#include <Bentley/BeNumerical.h>
#include <Bentley/Logging.h>
#include <GeomSerialization/GeomSerializationApi.h>
#include <DgnPlatform/DgnDb.h>
#include <memory>
#include <functional>
#include <vector>
#include <map>
#include <set>
#include <string>
#include <tuple>
#include <algorithm>
#include <cmath>
#include <limits>

BEGIN_BENTLEY_TERRAINMODEL_NAMESPACE

// Forward declarations
class DTMImpl;
class DTMFeature;
class DTMMesh;
class DTMPoint;

/*=======================================================================================*//**
 * @brief DTM Feature Type enumeration
 * @details Defines the different types of DTM features
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
enum class DTMFeatureType : int
{
    Unknown = 0,
    BreakLine = 1,
    ContourLine = 2,
    Void = 3,
    Island = 4,
    Hole = 5,
    Point = 6,
    Hull = 7
};

/*=======================================================================================*//**
 * @brief DTM Dynamic Feature Type enumeration
 * @details Defines the different types of dynamic DTM features
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
enum class DTMDynamicFeatureType : int
{
    Unknown = 0,
    Boundary = 1,
    Contour = 2,
    FlowLine = 3,
    Ridge = 4,
    Valley = 5
};

/*=======================================================================================*//**
 * @brief DTM State enumeration
 * @details Defines the current state of the DTM
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
enum class DTMState : int
{
    Data = 0,           // DTM contains data but is not triangulated
    Tin = 1,            // DTM is triangulated
    TinError = 2        // DTM has triangulation errors
};

/*=======================================================================================*//**
 * @brief Visibility Type enumeration
 * @details Defines visibility states for DTM features
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
enum class VisibilityType : int
{
    Visible = 0,        // Feature is visible
    Invisible = 1,      // Feature is invisible
    Partial = 2         // Feature is partially visible
};

/*=======================================================================================*//**
 * @brief DTM Feature Statistics Information
 * @details Contains statistical information about DTM features
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
struct DTMFeatureStatisticsInfo
{
    int64_t verticesCount = 0;
    int64_t trianglesCount = 0;
    int64_t triangleLinesCount = 0;
    int64_t featuresCount = 0;
    int64_t breakLinesCount = 0;
    int64_t contourLinesCount = 0;
    int64_t voidsCount = 0;
    int64_t islandsCount = 0;
    int64_t holesCount = 0;
    int64_t pointFeaturesCount = 0;
    bool hasHull = false;
};

/*=======================================================================================*//**
 * @brief DTM Standalone Feature Information
 * @details Contains information about a standalone DTM feature
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
class DTMStandAloneFeatureInfo
{
private:
    DTMFeatureId m_dtmFeatureId;
    DTMFeatureType m_dtmFeatureType;
    DTMUserTag m_dtmUserTag;
    std::vector<DPoint3d> m_points;

public:
    DTMStandAloneFeatureInfo(DTMFeatureId featureId, DTMFeatureType featureType, 
                           DTMUserTag userTag, const std::vector<DPoint3d>& points);

    DTMFeatureId GetFeatureId() const { return m_dtmFeatureId; }
    DTMFeatureType GetFeatureType() const { return m_dtmFeatureType; }
    DTMUserTag GetUserTag() const { return m_dtmUserTag; }
    const std::vector<DPoint3d>& GetPoints() const { return m_points; }
};

/*=======================================================================================*//**
 * @brief DTM Dynamic Feature Information
 * @details Contains information about a dynamic DTM feature
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
class DTMDynamicFeatureInfo
{
private:
    DTMDynamicFeatureType m_featureType;
    std::vector<DPoint3d> m_featurePoints;

public:
    DTMDynamicFeatureInfo(DTMDynamicFeatureType featureType, const std::vector<DPoint3d>& points);

    DTMDynamicFeatureType GetFeatureType() const { return m_featureType; }
    const std::vector<DPoint3d>& GetFeaturePoints() const { return m_featurePoints; }
};

/*=======================================================================================*//**
 * @brief Pond Calculation Result
 * @details Contains the results of pond calculation
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
class PondCalculation
{
private:
    bool m_calculated;
    double m_elevation;
    double m_depth;
    double m_area;
    double m_volume;
    std::vector<DTMDynamicFeatureInfo> m_pondFeatures;

public:
    PondCalculation(bool calculated, double elevation, double depth, double area, double volume, 
                   std::vector<DTMDynamicFeatureInfo> features);

    bool IsCalculated() const { return m_calculated; }
    double GetElevation() const { return m_elevation; }
    double GetDepth() const { return m_depth; }
    double GetArea() const { return m_area; }
    double GetVolume() const { return m_volume; }
    const std::vector<DTMDynamicFeatureInfo>& GetPondFeatures() const { return m_pondFeatures; }
};

/*=======================================================================================*//**
 * @brief Visibility Result
 * @details Contains the results of visibility calculation
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
class VisibilityResult
{
private:
    VisibilityType m_visibilityType;
    std::vector<DTMDynamicFeatureInfo> m_features;

public:
    VisibilityResult(VisibilityType visibility, std::vector<DTMDynamicFeatureInfo> features);

    VisibilityType GetVisibility() const { return m_visibilityType; }
    const std::vector<DTMDynamicFeatureInfo>& GetFeatures() const { return m_features; }
};

/*=======================================================================================*//**
 * @brief Cut Fill Result
 * @details Contains the results of cut and fill operations
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
class CutFillResult
{
private:
    double m_cutVolume;
    double m_fillVolume;
    double m_balanceVolume;
    double m_totalArea;
    double m_cutArea;
    double m_fillArea;

public:
    CutFillResult(double cutVolume, double fillVolume, double balanceVolume, 
                 double totalArea, double cutArea, double fillArea);

    double GetCutVolume() const { return m_cutVolume; }
    double GetFillVolume() const { return m_fillVolume; }
    double GetBalanceVolume() const { return m_balanceVolume; }
    double GetTotalArea() const { return m_totalArea; }
    double GetCutArea() const { return m_cutArea; }
    double GetFillArea() const { return m_fillArea; }
};

/*=======================================================================================*//**
 * @brief Point Catchment Result
 * @details Contains the results of point catchment tracing
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
class PointCatchmentResult
{
private:
    std::vector<DTMDynamicFeatureInfo> m_catchment;
    DPoint3d m_sumpPoint;

public:
    PointCatchmentResult(std::vector<DTMDynamicFeatureInfo> catchment, const DPoint3d& sumpPoint);

    const std::vector<DTMDynamicFeatureInfo>& GetCatchment() const { return m_catchment; }
    const DPoint3d& GetSumpPoint() const { return m_sumpPoint; }
};

/*=======================================================================================*//**
 * @brief Volume Polygon
 * @details Represents a polygon used in volume calculations
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
class VolumePolygon
{
private:
    std::vector<DPoint3d> m_points;

public:
    VolumePolygon(const DPoint3d* points, int numPoints);
    VolumePolygon(const std::vector<DPoint3d>& points);

    const std::vector<DPoint3d>& GetPoints() const { return m_points; }
    int GetNumPoints() const { return static_cast<int>(m_points.size()); }
};

/*=======================================================================================*//**
 * @brief Volume Result
 * @details Contains the results of volume calculations, inherits from CutFillResult
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
class VolumeResult : public CutFillResult
{
private:
    double m_fromArea;
    double m_toArea;
    int m_numCellsUsed;
    double m_cellArea;
    std::vector<VolumePolygon> m_volumePolygons;

public:
    VolumeResult(double cutVolume, double fillVolume, double balanceVolume,
                double totalArea, double cutArea, double fillArea,
                double fromArea, double toArea, int numCellsUsed, double cellArea,
                std::vector<VolumePolygon> volumePolygons);

    double GetFromArea() const { return m_fromArea; }
    double GetToArea() const { return m_toArea; }
    int GetNumCellsUsed() const { return m_numCellsUsed; }
    double GetCellArea() const { return m_cellArea; }
    const std::vector<VolumePolygon>& GetVolumePolygons() const { return m_volumePolygons; }
    int GetNumVolumePolygons() const { return static_cast<int>(m_volumePolygons.size()); }
};

/*=======================================================================================*//**
 * @brief Interpolation Result
 * @details Contains the results of interpolation operations
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
class InterpolationResult
{
private:
    int m_numDtmFeatures;
    int m_numDtmFeaturesInterpolated;

public:
    InterpolationResult(int numDtmFeatures, int numDtmFeaturesInterpolated);

    int GetNumDtmFeatures() const { return m_numDtmFeatures; }
    int GetNumDtmFeaturesInterpolated() const { return m_numDtmFeaturesInterpolated; }
};

/*=======================================================================================*//**
 * @brief Slope Area Result
 * @details Contains the results of slope area calculations
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
class SlopeAreaResult
{
private:
    double m_area;

public:
    SlopeAreaResult(double area);

    double GetArea() const { return m_area; }
};

/*=======================================================================================*//**
 * @brief Volume Range
 * @details Represents a volume range with low and high elevations
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
class VolumeRange
{
private:
    double m_low;
    double m_high;

public:
    VolumeRange(double low, double high);

    double GetLow() const { return m_low; }
    void SetLow(double value) { m_low = value; }

    double GetHigh() const { return m_high; }
    void SetHigh(double value) { m_high = value; }
};

/*=======================================================================================*//**
 * @brief DTM Duplicate Point Error
 * @details Contains information about duplicate point errors
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
class DTMDuplicatePointError : public std::exception
{
private:
    double m_x;
    double m_y;
    double m_z;
    DTMFeatureType m_dtmFeatureType;
    DTMFeatureId m_dtmFeatureId;
    DTMUserTag m_dtmUserTag;

public:
    DTMDuplicatePointError(double x, double y, double z, DTMFeatureType featureType,
                          DTMFeatureId featureId, DTMUserTag userTag);

    double GetX() const { return m_x; }
    double GetY() const { return m_y; }
    double GetZ() const { return m_z; }
    DTMFeatureType GetDtmFeatureType() const { return m_dtmFeatureType; }
    DTMFeatureId GetDtmFeatureId() const { return m_dtmFeatureId; }
    DTMUserTag GetDtmUserTag() const { return m_dtmUserTag; }

    const char* what() const noexcept override;
};

/*=======================================================================================*//**
 * @brief DTM Crossing Feature Error
 * @details Contains information about crossing feature errors
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
class DTMCrossingFeatureError : public std::exception
{
private:
    double m_intersectionX;
    double m_intersectionY;
    DTMFeatureType m_dtmFeatureType1;
    DTMFeatureId m_dtmFeatureId1;
    int64_t m_segmentOffset1;
    double m_elevation1;
    double m_distance1;
    DTMFeatureType m_dtmFeatureType2;
    DTMFeatureId m_dtmFeatureId2;
    int64_t m_segmentOffset2;
    double m_elevation2;
    double m_distance2;

public:
    DTMCrossingFeatureError(double intersectionX, double intersectionY,
                           DTMFeatureType featureType1, DTMFeatureId featureId1, int64_t segmentOffset1,
                           double elevation1, double distance1,
                           DTMFeatureType featureType2, DTMFeatureId featureId2, int64_t segmentOffset2,
                           double elevation2, double distance2);

    DPoint2d GetIntersectionPointXY() const { return DPoint2d::From(m_intersectionX, m_intersectionY); }
    DTMFeatureType GetTypeOfFeature1() const { return m_dtmFeatureType1; }
    DTMFeatureId GetIdOfFeature1() const { return m_dtmFeatureId1; }
    int64_t GetSegmentOffsetOnFeature1() const { return m_segmentOffset1; }
    double GetElevationOnFeature1() const { return m_elevation1; }
    double GetDistanceOnFeature1() const { return m_distance1; }
    DTMFeatureType GetTypeOfFeature2() const { return m_dtmFeatureType2; }
    DTMFeatureId GetIdOfFeature2() const { return m_dtmFeatureId2; }
    int64_t GetSegmentOffsetOnFeature2() const { return m_segmentOffset2; }
    double GetElevationOnFeature2() const { return m_elevation2; }
    double GetDistanceOnFeature2() const { return m_distance2; }

    const char* what() const noexcept override;
};

END_BENTLEY_TERRAINMODEL_NAMESPACE
