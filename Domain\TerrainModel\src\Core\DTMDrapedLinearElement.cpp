/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See LICENSE.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#include "TerrainModel/Core/DTMDrapedLinearElement.h"
#include "TerrainModel/Core/DTM.h"
#include <vector>
#include <algorithm>

BEGIN_BENTLEY_TERRAINMODEL_NAMESPACE

// DTMDrapePointFeature 实现

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
DTMDrapePointFeature::DTMDrapePointFeature(int featureIndex, DTMFeatureType featureType, 
                                         DTMUserTag userTag, DTMFeatureId featureId, 
                                         int priorPoint, int nextPoint) :
    m_dtmFeatureIndex(featureIndex),
    m_dtmFeatureType(featureType),
    m_dtmUserTag(userTag),
    m_dtmFeatureId(featureId),
    m_priorFeaturePoint(priorPoint),
    m_nextFeaturePoint(nextPoint)
{
}

// DTMDrapedLinearElementPoint 实现

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
DTMDrapedLinearElementPoint::DTMDrapedLinearElementPoint(DTMDrapedLinearElementPointCode code, 
                                                      double distance, 
                                                      const std::tuple<double, double, double>& coords,
                                                      const std::vector<DTMFeatureId>& featureIds,
                                                      const std::vector<DTMDrapePointFeature>& features) :
    m_code(code),
    m_distanceAlong(distance),
    m_coordinates(coords),
    m_featureIds(featureIds),
    m_features(features)
{
}

// DTMDrapedLinearElementImpl 实现类
class DTMDrapedLinearElementImpl
{
private:
    std::vector<DTMDrapedLinearElementPoint> m_points;
    
public:
    // 构造函数
    DTMDrapedLinearElementImpl()
    {
    }
    
    // 添加点
    void AddPoint(const DTMDrapedLinearElementPoint& point)
    {
        m_points.push_back(point);
    }
    
    // 获取点数量
    int GetPointCount() const
    {
        return static_cast<int>(m_points.size());
    }
    
    // 根据索引获取点
    DTMDrapedLinearElementPoint GetPointByIndex(int index) const
    {
        if (index >= 0 && index < static_cast<int>(m_points.size()))
            return m_points[index];
            
        // 返回一个默认点作为错误处理
        return DTMDrapedLinearElementPoint(
            DTMDrapedLinearElementPointCode::External,
            0.0,
            std::make_tuple(0.0, 0.0, 0.0),
            std::vector<DTMFeatureId>(),
            std::vector<DTMDrapePointFeature>()
        );
    }
};

// DTMDrapedLinearElementPointIteratorImpl 实现类
class DTMDrapedLinearElementPointIteratorImpl
{
private:
    const DTMDrapedLinearElement* m_element;
    int m_index;
    int m_count;
    
public:
    // 构造函数
    DTMDrapedLinearElementPointIteratorImpl(const DTMDrapedLinearElement& element) :
        m_element(&element),
        m_index(-1),
        m_count(element.GetPointCount())
    {
    }
    
    // 移动到下一点
    bool MoveNext()
    {
        if (m_index < m_count - 1)
        {
            m_index++;
            return true;
        }
        
        return false;
    }
    
    // 重置迭代器
    void Reset()
    {
        m_index = -1;
    }
    
    // 获取当前点
    DTMDrapedLinearElementPoint GetCurrent() const
    {
        if (m_index >= 0 && m_index < m_count)
            return m_element->GetPointByIndex(m_index);
            
        // 返回一个默认点作为错误处理
        return DTMDrapedLinearElementPoint(
            DTMDrapedLinearElementPointCode::External,
            0.0,
            std::make_tuple(0.0, 0.0, 0.0),
            std::vector<DTMFeatureId>(),
            std::vector<DTMDrapePointFeature>()
        );
    }
    
    // 检查是否有效
    bool IsValid() const
    {
        return (m_index >= 0 && m_index < m_count);
    }
};

// DTMDrapedLinearElementPointIterator 实现

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
DTMDrapedLinearElementPointIterator::DTMDrapedLinearElementPointIterator(const DTMDrapedLinearElement& element) :
    m_impl(std::make_unique<DTMDrapedLinearElementPointIteratorImpl>(element))
{
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
DTMDrapedLinearElementPointIterator::~DTMDrapedLinearElementPointIterator() = default;

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
bool DTMDrapedLinearElementPointIterator::MoveNext()
{
    return m_impl->MoveNext();
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
void DTMDrapedLinearElementPointIterator::Reset()
{
    m_impl->Reset();
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
DTMDrapedLinearElementPoint DTMDrapedLinearElementPointIterator::GetCurrent() const
{
    return m_impl->GetCurrent();
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
bool DTMDrapedLinearElementPointIterator::IsValid() const
{
    return m_impl->IsValid();
}

// DTMDrapedLinearElement 实现

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
DTMDrapedLinearElement::DTMDrapedLinearElement() :
    m_impl(std::make_unique<DTMDrapedLinearElementImpl>())
{
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
DTMDrapedLinearElement::~DTMDrapedLinearElement() = default;

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
std::unique_ptr<DTMDrapedLinearElement> DTMDrapedLinearElement::Create(const DTM& dtm, 
                                                                     const std::vector<std::tuple<double, double>>& points)
{
    // 创建新的拖曳线性元素
    auto element = std::make_unique<DTMDrapedLinearElement>();
    
    // 计算累计距离
    double cumulativeDistance = 0.0;
    double prevX = 0.0, prevY = 0.0;
    bool isFirst = true;
    
    // 为每个点创建拖曳点
    for (const auto& point : points)
    {
        double x = std::get<0>(point);
        double y = std::get<1>(point);
        
        // 计算累计距离
        if (!isFirst)
        {
            double dx = x - prevX;
            double dy = y - prevY;
            cumulativeDistance += std::sqrt(dx * dx + dy * dy);
        }
        else
        {
            isFirst = false;
        }
        
        prevX = x;
        prevY = y;
        
        // 查询Z坐标（在实际实现中应调用DTM获取高程）
        double z = 0.0;  // 占位，实际应调用dtm.GetElevationAtPoint(x, y, z);
        
        // 创建拖曳点（简化版本）
        auto drapePoint = DTMDrapedLinearElementPoint(
            DTMDrapedLinearElementPointCode::Triangle,  // 假设所有点都在三角形上
            cumulativeDistance,
            std::make_tuple(x, y, z),
            std::vector<DTMFeatureId>(),  // 空特征ID列表
            std::vector<DTMDrapePointFeature>()  // 空特征列表
        );
        
        // 添加到拖曳线
        element->m_impl->AddPoint(drapePoint);
    }
    
    return element;
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
int DTMDrapedLinearElement::GetPointCount() const
{
    return m_impl->GetPointCount();
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
DTMDrapedLinearElementPoint DTMDrapedLinearElement::GetPointByIndex(int index) const
{
    return m_impl->GetPointByIndex(index);
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
std::unique_ptr<DTMDrapedLinearElementPointIterator> DTMDrapedLinearElement::CreateIterator() const
{
    // 创建一个新的迭代器并返回
    return std::make_unique<DTMDrapedLinearElementPointIterator>(*this);
}

END_BENTLEY_TERRAINMODEL_NAMESPACE

