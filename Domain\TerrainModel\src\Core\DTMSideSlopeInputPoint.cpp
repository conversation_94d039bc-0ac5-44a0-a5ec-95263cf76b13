/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See LICENSE.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#include "TerrainModel/Core/DTMSideSlopeInputPoint.h"
#include <cmath>

BEGIN_BENTLEY_TERRAINMODEL_NAMESPACE

// 构造函数和析构函数
DTMSideSlopeInputPoint::DTMSideSlopeInputPoint() 
    : m_x(0.0), m_y(0.0), m_z(0.0), m_width(0.0)
{
}

DTMSideSlopeInputPoint::DTMSideSlopeInputPoint(double x, double y, double z, double width)
    : m_x(x), m_y(y), m_z(z), m_width(width)
{
}

DTMSideSlopeInputPoint::DTMSideSlopeInputPoint(const DTMSideSlopeInputPoint& other)
    : m_x(other.m_x), m_y(other.m_y), m_z(other.m_z), m_width(other.m_width)
{
}

DTMSideSlopeInputPoint::DTMSideSlopeInputPoint(DTMSideSlopeInputPoint&& other) noexcept
    : m_x(other.m_x), m_y(other.m_y), m_z(other.m_z), m_width(other.m_width)
{
}

DTMSideSlopeInputPoint& DTMSideSlopeInputPoint::operator=(const DTMSideSlopeInputPoint& other)
{
    if (this != &other)
    {
        m_x = other.m_x;
        m_y = other.m_y;
        m_z = other.m_z;
        m_width = other.m_width;
    }
    return *this;
}

DTMSideSlopeInputPoint& DTMSideSlopeInputPoint::operator=(DTMSideSlopeInputPoint&& other) noexcept
{
    if (this != &other)
    {
        m_x = other.m_x;
        m_y = other.m_y;
        m_z = other.m_z;
        m_width = other.m_width;
    }
    return *this;
}

DTMSideSlopeInputPoint::~DTMSideSlopeInputPoint()
{
}

// 实用方法
double DTMSideSlopeInputPoint::DistanceTo(const DTMSideSlopeInputPoint& other) const
{
    double dx = m_x - other.m_x;
    double dy = m_y - other.m_y;
    double dz = m_z - other.m_z;
    
    return std::sqrt(dx * dx + dy * dy + dz * dz);
}

double DTMSideSlopeInputPoint::HorizontalDistanceTo(const DTMSideSlopeInputPoint& other) const
{
    double dx = m_x - other.m_x;
    double dy = m_y - other.m_y;
    
    return std::sqrt(dx * dx + dy * dy);
}

double DTMSideSlopeInputPoint::AzimuthTo(const DTMSideSlopeInputPoint& other) const
{
    double dx = other.m_x - m_x;
    double dy = other.m_y - m_y;
    
    double azimuth = std::atan2(dy, dx);
    
    // 将结果转换为[0, 2π]范围
    if (azimuth < 0.0)
        azimuth += 2.0 * 3.14159265358979323846;
    
    return azimuth;
}

END_BENTLEY_TERRAINMODEL_NAMESPACE

