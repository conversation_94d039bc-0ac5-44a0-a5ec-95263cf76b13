﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="mscorlib">
      <CopyLocalSatelliteAssemblies>true</CopyLocalSatelliteAssemblies>
      <ReferenceOutputAssembly>true</ReferenceOutputAssembly>
    </Reference>
    <Reference Include="System">
      <CopyLocalSatelliteAssemblies>true</CopyLocalSatelliteAssemblies>
      <ReferenceOutputAssembly>true</ReferenceOutputAssembly>
    </Reference>
    <Reference Include="System.Data">
      <CopyLocalSatelliteAssemblies>true</CopyLocalSatelliteAssemblies>
      <ReferenceOutputAssembly>true</ReferenceOutputAssembly>
    </Reference>
    <Reference Include="System.Drawing">
      <CopyLocalSatelliteAssemblies>true</CopyLocalSatelliteAssemblies>
      <ReferenceOutputAssembly>true</ReferenceOutputAssembly>
    </Reference>
    <Reference Include="System.Windows.Forms">
      <CopyLocalSatelliteAssemblies>true</CopyLocalSatelliteAssemblies>
      <ReferenceOutputAssembly>true</ReferenceOutputAssembly>
    </Reference>
    <Reference Include="System.Xml">
      <CopyLocalSatelliteAssemblies>true</CopyLocalSatelliteAssemblies>
      <ReferenceOutputAssembly>true</ReferenceOutputAssembly>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="Caching.cpp" />
    <ClCompile Include="DTM.cpp" />
    <ClCompile Include="DTMDrapedLinearElement.cpp" />
    <ClCompile Include="DTMFeature.cpp" />
    <ClCompile Include="DTMFeatureEnumerator.cpp" />
    <ClCompile Include="DTMHelpers.cpp" />
    <ClCompile Include="DTMMesh.cpp" />
    <ClCompile Include="DTMMeshEnumerator.cpp" />
    <ClCompile Include="DTMPond.cpp" />
    <ClCompile Include="DTMSideSlopeInput.cpp" />
    <ClCompile Include="DTMSideSlopeInputPoint.cpp" />
    <ClCompile Include="DTMTinEditor.cpp" />
    <ClCompile Include="WaterAnalysis.cpp" />
    <ClCompile Include="Stdafx.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="Bentley.Civil.DTM.h" />
    <ClInclude Include="Caching.h" />
    <ClInclude Include="DTM.h" />
    <ClInclude Include="DTMDelegates.h" />
    <ClInclude Include="DTMDrapedLinearElement.h" />
    <ClInclude Include="DTMException.h" />
    <ClInclude Include="DTMFeature.h" />
    <ClInclude Include="DTMFeatureEnumerator.h" />
    <ClInclude Include="DTMHelpers.h" />
    <ClInclude Include="DTMMesh.h" />
    <ClInclude Include="DTMMeshEnumerator.h" />
    <ClInclude Include="DTMSideSlopeInput.h" />
    <ClInclude Include="DTMSideSlopeInputPoint.h" />
    <ClInclude Include="DTMTinEditor.h" />
    <ClInclude Include="Stdafx.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="TerrainModelNET.mke" />
    <None Include="TerrainModelNET.mki" />
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{DCC5E6AA-A8BE-429B-A4AA-46BD1744E540}</ProjectGuid>
    <Keyword>MakeFileProj</Keyword>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CLRSupport>true</CLRSupport>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <PlatformToolset>v110</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <PlatformToolset>v110</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <NMakeBuildCommandLine>python.exe $(SrcBsiCommon)build\BentleyBuild.py -p TerrainModelNET -f TerrainModel -r TerrainModel re TerrainModelNET -x64    </NMakeBuildCommandLine>
    <NMakeOutput>$(OutRoot)Winx64\build\TerrainModel\TerrainModelNET\Bentley.TerrainModelNET.dll</NMakeOutput>
    <BaseIntermediateOutputPath>$(OutRoot)VS\$(Configuration)\</BaseIntermediateOutputPath>
    <NMakeCleanCommandLine>python.exe $(SrcBsiCommon)build\BentleyBuild.py -p TerrainModelNET -f TerrainModel -r TerrainModel re TerrainModelNET -x64 -c    </NMakeCleanCommandLine>
    <NMakeReBuildCommandLine>python.exe $(SrcBsiCommon)build\BentleyBuild.py -p TerrainModelNET -f TerrainModel -r TerrainModel re TerrainModelNET -x64  -d"BMAKE_OPT=$(BMAKE_OPT) -a"</NMakeReBuildCommandLine>
    <NMakePreprocessorDefinitions>WIN32;winNT;_VISCXX;_CONVERSION_DONT_USE_THREAD_LOCALE;_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES=1;_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES_COUNT=1;_SECURE_SCL_THROWS=1;_SECURE_SCL=0;WIN32_LEAN_AND_MEAN;WINVER=0x0501; _WIN32_WINNT=0x0501;_WIN32_IE=0x0501;DEBUG;WIN32;_DEBUG;$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <NMakeIncludeSearchPath>$(ProjectDir);$(ProjectDir)PrivateAPI;$(ProjectDir)PublicAPI;$(ProjectDir)cppwrappers;$(OutRoot)Winx64\BuildContexts\TerrainModel\PublicAPI</NMakeIncludeSearchPath>
    <OutDir>$(OutRoot)VS\$(Configuration)\</OutDir>
    <IntDir>$(OutRoot)VS\$(Configuration)\</IntDir>
    <AdditionalOptions>/clr</AdditionalOptions>
    <NMakeAssemblySearchPath>$(OutRoot)Winx64\BuildContexts\TerrainModel\SubParts\Assemblies</NMakeAssemblySearchPath>
    <SourcePath>$(SourcePath)</SourcePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <NMakeBuildCommandLine>
      python.exe $(SrcBsiCommon)build\BentleyBuild.py rebuild -x64 TerrainModel:TerrainModelNET
    </NMakeBuildCommandLine>
    <NMakeOutput>$(OutRoot)Winx64\build\TerrainModel\TerrainModelNET\Bentley.TerrainModelNET.dll</NMakeOutput>
    <BaseIntermediateOutputPath>$(OutRoot)VS\$(Configuration)\</BaseIntermediateOutputPath>
    <NMakeCleanCommandLine>
      python.exe $(SrcBsiCommon)build\BentleyBuild.py rebuild -x64 -c TerrainModel:TerrainModelNET
    </NMakeCleanCommandLine>
    <NMakeReBuildCommandLine>
      python.exe $(SrcBsiCommon)build\BentleyBuild.py rebuild -x64 -c TerrainModel:TerrainModelNET
      python.exe $(SrcBsiCommon)build\BentleyBuild.py rebuild -x64 TerrainModel:TerrainModelNET
    </NMakeReBuildCommandLine>
    <NMakePreprocessorDefinitions>WIN32;winNT;_VISCXX;_CONVERSION_DONT_USE_THREAD_LOCALE;_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES=1;_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES_COUNT=1;_SECURE_SCL_THROWS=1;_SECURE_SCL=0;WIN32_LEAN_AND_MEAN;WINVER=0x0501; _WIN32_WINNT=0x0501;_WIN32_IE=0x0501;NDEBUG;WIN32;$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <NMakeIncludeSearchPath>$(ProjectDir);$(ProjectDir)PrivateAPI;$(ProjectDir)PublicAPI;$(ProjectDir)cppwrappers;$(OutRoot)Winx64\BuildContexts\TerrainModel\PublicAPI</NMakeIncludeSearchPath>
    <OutDir>$(OutRoot)VS\$(Configuration)\</OutDir>
    <IntDir>$(OutRoot)VS\$(Configuration)\</IntDir>
    <AdditionalOptions>/clr</AdditionalOptions>
  </PropertyGroup>
  <ItemDefinitionGroup>
  </ItemDefinitionGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>