﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{6ED63E85-1562-3683-88F9-3773AFAC31F9}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>TerrainModel</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\dev\realitycoreAdon-cmake\build\bin\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">TerrainModel.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">TerrainModel</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\dev\realitycoreAdon-cmake\build\bin\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">TerrainModel.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">TerrainModel</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\dev\realitycoreAdon-cmake\build\bin\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">TerrainModel.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">TerrainModel</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\dev\realitycoreAdon-cmake\build\bin\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">TerrainModel.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">TerrainModel</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <PropertyGroup />
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\sdk\native\include;D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include;D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zm150</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4251;4275;4251;4275</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp14</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;TERRAINMODEL_EXPORT=__declspec(dllexport);_CONVERSION_DONT_USE_THREAD_LOCALE;_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES=1;_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES_COUNT=1;_SECURE_SCL_THROWS=1;_SECURE_SCL=0;WIN32_LEAN_AND_MEAN;WINVER=0x0501;_WIN32_WINNT=0x0501;_WIN32_IE=0x0501;_USRDLL;WIN32=1;DLL_EXPORT=1;BE_USE_DYNAMIC_PRECOMPILED_HEADER=1;BENTLEY_WIN32;__TERRAINMODEL_BUILD__;_ITERATOR_DEBUG_LEVEL=0;CMAKE_INTDIR="Debug";TerrainModel_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;TERRAINMODEL_EXPORT=__declspec(dllexport);_CONVERSION_DONT_USE_THREAD_LOCALE;_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES=1;_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES_COUNT=1;_SECURE_SCL_THROWS=1;_SECURE_SCL=0;WIN32_LEAN_AND_MEAN;WINVER=0x0501;_WIN32_WINNT=0x0501;_WIN32_IE=0x0501;_USRDLL;WIN32=1;DLL_EXPORT=1;BE_USE_DYNAMIC_PRECOMPILED_HEADER=1;BENTLEY_WIN32;__TERRAINMODEL_BUILD__;_ITERATOR_DEBUG_LEVEL=0;CMAKE_INTDIR=\"Debug\";TerrainModel_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\sdk\native\include;D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include;D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\sdk\native\include;D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include;D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreLinkEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Auto build dll exports</Message>
      <Command>setlocal
cd D:\dev\realitycoreAdon-cmake\build\Domain\TerrainModel
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E __create_def D:/dev/realitycoreAdon-cmake/build/Domain/TerrainModel/TerrainModel.dir/Debug/exports.def D:/dev/realitycoreAdon-cmake/build/Domain/TerrainModel/TerrainModel.dir/Debug//objects.txt
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreLinkEvent>
    <Link>
      <AdditionalDependencies>D:\sdk\native\lib\TerrainModelCore.lib;D:\sdk\native\lib\TerrainModelFormats.lib;D:\sdk\native\lib\Bentley.lib;D:\sdk\native\lib\ECObjects.lib;D:\sdk\native\lib\BentleyGeom.lib;D:\sdk\native\lib\iModelPlatform.lib;D:\sdk\native\lib\BentleyGeomSerialization.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>D:/sdk/native/lib;D:/sdk/native/lib/$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/dev/realitycoreAdon-cmake/build/lib/Debug/TerrainModel.lib</ImportLibrary>
      <ModuleDefinitionFile>D:/dev/realitycoreAdon-cmake/build/Domain/TerrainModel/TerrainModel.dir/Debug/exports.def</ModuleDefinitionFile>
      <ProgramDataBaseFile>D:/dev/realitycoreAdon-cmake/build/bin/Debug/TerrainModel.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\sdk\native\include;D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include;D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zm150</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>4251;4275;4251;4275</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp14</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;TERRAINMODEL_EXPORT=__declspec(dllexport);_CONVERSION_DONT_USE_THREAD_LOCALE;_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES=1;_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES_COUNT=1;_SECURE_SCL_THROWS=1;_SECURE_SCL=0;WIN32_LEAN_AND_MEAN;WINVER=0x0501;_WIN32_WINNT=0x0501;_WIN32_IE=0x0501;_USRDLL;WIN32=1;DLL_EXPORT=1;BE_USE_DYNAMIC_PRECOMPILED_HEADER=1;BENTLEY_WIN32;__TERRAINMODEL_BUILD__;_ITERATOR_DEBUG_LEVEL=0;CMAKE_INTDIR="Release";TerrainModel_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;TERRAINMODEL_EXPORT=__declspec(dllexport);_CONVERSION_DONT_USE_THREAD_LOCALE;_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES=1;_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES_COUNT=1;_SECURE_SCL_THROWS=1;_SECURE_SCL=0;WIN32_LEAN_AND_MEAN;WINVER=0x0501;_WIN32_WINNT=0x0501;_WIN32_IE=0x0501;_USRDLL;WIN32=1;DLL_EXPORT=1;BE_USE_DYNAMIC_PRECOMPILED_HEADER=1;BENTLEY_WIN32;__TERRAINMODEL_BUILD__;_ITERATOR_DEBUG_LEVEL=0;CMAKE_INTDIR=\"Release\";TerrainModel_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\sdk\native\include;D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include;D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\sdk\native\include;D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include;D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreLinkEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Auto build dll exports</Message>
      <Command>setlocal
cd D:\dev\realitycoreAdon-cmake\build\Domain\TerrainModel
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E __create_def D:/dev/realitycoreAdon-cmake/build/Domain/TerrainModel/TerrainModel.dir/Release/exports.def D:/dev/realitycoreAdon-cmake/build/Domain/TerrainModel/TerrainModel.dir/Release//objects.txt
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreLinkEvent>
    <Link>
      <AdditionalDependencies>D:\sdk\native\lib\TerrainModelCore.lib;D:\sdk\native\lib\TerrainModelFormats.lib;D:\sdk\native\lib\Bentley.lib;D:\sdk\native\lib\ECObjects.lib;D:\sdk\native\lib\BentleyGeom.lib;D:\sdk\native\lib\iModelPlatform.lib;D:\sdk\native\lib\BentleyGeomSerialization.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>D:/sdk/native/lib;D:/sdk/native/lib/$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/dev/realitycoreAdon-cmake/build/lib/Release/TerrainModel.lib</ImportLibrary>
      <ModuleDefinitionFile>D:/dev/realitycoreAdon-cmake/build/Domain/TerrainModel/TerrainModel.dir/Release/exports.def</ModuleDefinitionFile>
      <ProgramDataBaseFile>D:/dev/realitycoreAdon-cmake/build/bin/Release/TerrainModel.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\sdk\native\include;D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include;D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zm150</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>4251;4275;4251;4275</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp14</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;TERRAINMODEL_EXPORT=__declspec(dllexport);_CONVERSION_DONT_USE_THREAD_LOCALE;_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES=1;_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES_COUNT=1;_SECURE_SCL_THROWS=1;_SECURE_SCL=0;WIN32_LEAN_AND_MEAN;WINVER=0x0501;_WIN32_WINNT=0x0501;_WIN32_IE=0x0501;_USRDLL;WIN32=1;DLL_EXPORT=1;BE_USE_DYNAMIC_PRECOMPILED_HEADER=1;BENTLEY_WIN32;__TERRAINMODEL_BUILD__;_ITERATOR_DEBUG_LEVEL=0;CMAKE_INTDIR="MinSizeRel";TerrainModel_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;TERRAINMODEL_EXPORT=__declspec(dllexport);_CONVERSION_DONT_USE_THREAD_LOCALE;_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES=1;_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES_COUNT=1;_SECURE_SCL_THROWS=1;_SECURE_SCL=0;WIN32_LEAN_AND_MEAN;WINVER=0x0501;_WIN32_WINNT=0x0501;_WIN32_IE=0x0501;_USRDLL;WIN32=1;DLL_EXPORT=1;BE_USE_DYNAMIC_PRECOMPILED_HEADER=1;BENTLEY_WIN32;__TERRAINMODEL_BUILD__;_ITERATOR_DEBUG_LEVEL=0;CMAKE_INTDIR=\"MinSizeRel\";TerrainModel_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\sdk\native\include;D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include;D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\sdk\native\include;D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include;D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreLinkEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Auto build dll exports</Message>
      <Command>setlocal
cd D:\dev\realitycoreAdon-cmake\build\Domain\TerrainModel
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E __create_def D:/dev/realitycoreAdon-cmake/build/Domain/TerrainModel/TerrainModel.dir/MinSizeRel/exports.def D:/dev/realitycoreAdon-cmake/build/Domain/TerrainModel/TerrainModel.dir/MinSizeRel//objects.txt
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreLinkEvent>
    <Link>
      <AdditionalDependencies>D:\sdk\native\lib\TerrainModelCore.lib;D:\sdk\native\lib\TerrainModelFormats.lib;D:\sdk\native\lib\Bentley.lib;D:\sdk\native\lib\ECObjects.lib;D:\sdk\native\lib\BentleyGeom.lib;D:\sdk\native\lib\iModelPlatform.lib;D:\sdk\native\lib\BentleyGeomSerialization.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>D:/sdk/native/lib;D:/sdk/native/lib/$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/dev/realitycoreAdon-cmake/build/lib/MinSizeRel/TerrainModel.lib</ImportLibrary>
      <ModuleDefinitionFile>D:/dev/realitycoreAdon-cmake/build/Domain/TerrainModel/TerrainModel.dir/MinSizeRel/exports.def</ModuleDefinitionFile>
      <ProgramDataBaseFile>D:/dev/realitycoreAdon-cmake/build/bin/MinSizeRel/TerrainModel.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\sdk\native\include;D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include;D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /Zm150</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4251;4275;4251;4275</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp14</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;TERRAINMODEL_EXPORT=__declspec(dllexport);_CONVERSION_DONT_USE_THREAD_LOCALE;_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES=1;_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES_COUNT=1;_SECURE_SCL_THROWS=1;_SECURE_SCL=0;WIN32_LEAN_AND_MEAN;WINVER=0x0501;_WIN32_WINNT=0x0501;_WIN32_IE=0x0501;_USRDLL;WIN32=1;DLL_EXPORT=1;BE_USE_DYNAMIC_PRECOMPILED_HEADER=1;BENTLEY_WIN32;__TERRAINMODEL_BUILD__;_ITERATOR_DEBUG_LEVEL=0;CMAKE_INTDIR="RelWithDebInfo";TerrainModel_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;TERRAINMODEL_EXPORT=__declspec(dllexport);_CONVERSION_DONT_USE_THREAD_LOCALE;_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES=1;_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES_COUNT=1;_SECURE_SCL_THROWS=1;_SECURE_SCL=0;WIN32_LEAN_AND_MEAN;WINVER=0x0501;_WIN32_WINNT=0x0501;_WIN32_IE=0x0501;_USRDLL;WIN32=1;DLL_EXPORT=1;BE_USE_DYNAMIC_PRECOMPILED_HEADER=1;BENTLEY_WIN32;__TERRAINMODEL_BUILD__;_ITERATOR_DEBUG_LEVEL=0;CMAKE_INTDIR=\"RelWithDebInfo\";TerrainModel_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\sdk\native\include;D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include;D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\sdk\native\include;D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include;D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreLinkEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Auto build dll exports</Message>
      <Command>setlocal
cd D:\dev\realitycoreAdon-cmake\build\Domain\TerrainModel
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E __create_def D:/dev/realitycoreAdon-cmake/build/Domain/TerrainModel/TerrainModel.dir/RelWithDebInfo/exports.def D:/dev/realitycoreAdon-cmake/build/Domain/TerrainModel/TerrainModel.dir/RelWithDebInfo//objects.txt
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreLinkEvent>
    <Link>
      <AdditionalDependencies>D:\sdk\native\lib\TerrainModelCore.lib;D:\sdk\native\lib\TerrainModelFormats.lib;D:\sdk\native\lib\Bentley.lib;D:\sdk\native\lib\ECObjects.lib;D:\sdk\native\lib\BentleyGeom.lib;D:\sdk\native\lib\iModelPlatform.lib;D:\sdk\native\lib\BentleyGeomSerialization.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>D:/sdk/native/lib;D:/sdk/native/lib/$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/dev/realitycoreAdon-cmake/build/lib/RelWithDebInfo/TerrainModel.lib</ImportLibrary>
      <ModuleDefinitionFile>D:/dev/realitycoreAdon-cmake/build/Domain/TerrainModel/TerrainModel.dir/RelWithDebInfo/exports.def</ModuleDefinitionFile>
      <ProgramDataBaseFile>D:/dev/realitycoreAdon-cmake/build/bin/RelWithDebInfo/TerrainModel.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/dev/realitycoreAdon-cmake/Domain/TerrainModel/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/dev/realitycoreAdon-cmake -BD:/dev/realitycoreAdon-cmake/build --check-stamp-file D:/dev/realitycoreAdon-cmake/build/Domain/TerrainModel/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\dev\realitycoreAdon-cmake\build\Domain\TerrainModel\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/dev/realitycoreAdon-cmake/Domain/TerrainModel/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/dev/realitycoreAdon-cmake -BD:/dev/realitycoreAdon-cmake/build --check-stamp-file D:/dev/realitycoreAdon-cmake/build/Domain/TerrainModel/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\dev\realitycoreAdon-cmake\build\Domain\TerrainModel\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/dev/realitycoreAdon-cmake/Domain/TerrainModel/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/dev/realitycoreAdon-cmake -BD:/dev/realitycoreAdon-cmake/build --check-stamp-file D:/dev/realitycoreAdon-cmake/build/Domain/TerrainModel/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\dev\realitycoreAdon-cmake\build\Domain\TerrainModel\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/dev/realitycoreAdon-cmake/Domain/TerrainModel/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/dev/realitycoreAdon-cmake -BD:/dev/realitycoreAdon-cmake/build --check-stamp-file D:/dev/realitycoreAdon-cmake/build/Domain/TerrainModel/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\dev\realitycoreAdon-cmake\build\Domain\TerrainModel\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\src\Core\DTM.cpp" />
    <ClCompile Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\src\Core\DTMFeature.cpp" />
    <ClCompile Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\src\Core\DTMMesh.cpp" />
    <ClCompile Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\src\Core\DTMPond.cpp" />
    <ClCompile Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\src\Core\DTMTinEditor.cpp" />
    <ClCompile Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\src\Core\DTMHelpers.cpp" />
    <ClCompile Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\src\Core\DTMSideSlopeInput.cpp" />
    <ClCompile Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\src\Core\DTMSideSlopeInputPoint.cpp" />
    <ClCompile Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\src\Core\DTMDrapedLinearElement.cpp" />
    <ClCompile Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\src\Core\Caching.cpp" />
    <ClCompile Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\src\Core\DTMFeatureEnumerator.cpp" />
    <ClCompile Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\src\Core\DTMMeshEnumerator.cpp" />
    <ClCompile Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\src\Drainage\WaterAnalysis.cpp" />
    <ClInclude Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include\TerrainModel\TerrainModel.h" />
    <ClInclude Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include\TerrainModel\Core\DTM.h" />
    <ClInclude Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include\TerrainModel\Core\DTMFeature.h" />
    <ClInclude Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include\TerrainModel\Core\DTMMesh.h" />
    <ClInclude Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include\TerrainModel\Core\DTMPond.h" />
    <ClInclude Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include\TerrainModel\Core\DTMTinEditor.h" />
    <ClInclude Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include\TerrainModel\Core\DTMHelpers.h" />
    <ClInclude Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include\TerrainModel\Core\DTMSideSlopeInput.h" />
    <ClInclude Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include\TerrainModel\Core\DTMSideSlopeInputPoint.h" />
    <ClInclude Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include\TerrainModel\Core\DTMDrapedLinearElement.h" />
    <ClInclude Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include\TerrainModel\Core\DTMDelegates.h" />
    <ClInclude Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include\TerrainModel\Core\DTMException.h" />
    <ClInclude Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include\TerrainModel\Core\Caching.h" />
    <ClInclude Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include\TerrainModel\Core\DTMFeatureEnumerator.h" />
    <ClInclude Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include\TerrainModel\Core\DTMMeshEnumerator.h" />
    <ClInclude Include="D:\dev\realitycoreAdon-cmake\Domain\TerrainModel\include\TerrainModel\Drainage\WaterAnalysis.h" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\dev\realitycoreAdon-cmake\build\ZERO_CHECK.vcxproj">
      <Project>{40B1800F-E786-3125-87A1-0EC756D3C89D}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>