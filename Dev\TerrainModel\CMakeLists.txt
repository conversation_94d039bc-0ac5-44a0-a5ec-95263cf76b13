cmake_minimum_required(VERSION 3.14)

project(TerrainModel
    VERSION 1.0.0
    LANGUAGES CXX
    DESCRIPTION "Modern C++ Terrain Model Library"
)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 编译器特定设置
if(MSVC)
    # MSVC 编译器设置
    add_compile_options(/W4 /permissive-)
    add_compile_definitions(
        _CRT_SECURE_NO_WARNINGS
        NOMINMAX
        WIN32_LEAN_AND_MEAN
        TERRAINMODEL_EXPORTS
    )
else()
    # GCC/Clang 编译器设置
    add_compile_options(-Wall -Wextra -Wpedantic)
    add_compile_definitions(TERRAINMODEL_EXPORTS)
endif()

# 预处理器定义
add_definitions(
    -D__TERRAINMODEL_BUILD__
    -DBENTLEY_WIN32
)

# 查找依赖包
find_package(Threads REQUIRED)

# 定义路径变量
set(INCLUDE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/include)
set(SRC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/src)
set(TESTS_DIR ${CMAKE_CURRENT_SOURCE_DIR}/tests)

# 核心头文件
set(CORE_HEADERS
    ${INCLUDE_DIR}/TerrainModel/TerrainModel.h
    ${INCLUDE_DIR}/TerrainModel/Core/DTM.h
    ${INCLUDE_DIR}/TerrainModel/Core/DTMException.h
    ${INCLUDE_DIR}/TerrainModel/Core/DTMFeature.h
    ${INCLUDE_DIR}/TerrainModel/Core/DTMMesh.h
    ${INCLUDE_DIR}/TerrainModel/Core/DTMHelpers.h
    ${INCLUDE_DIR}/TerrainModel/Core/DTMDelegates.h
    ${INCLUDE_DIR}/TerrainModel/Core/DTMTinEditor.h
    ${INCLUDE_DIR}/TerrainModel/Core/DTMPond.h
    ${INCLUDE_DIR}/TerrainModel/Core/DTMFeatureEnumerator.h
    ${INCLUDE_DIR}/TerrainModel/Core/DTMMeshEnumerator.h
    ${INCLUDE_DIR}/TerrainModel/Core/DTMSideSlopeInput.h
    ${INCLUDE_DIR}/TerrainModel/Core/DTMSideSlopeInputPoint.h
    ${INCLUDE_DIR}/TerrainModel/Core/DTMDrapedLinearElement.h
    ${INCLUDE_DIR}/TerrainModel/Core/Caching.h
)

# 排水分析头文件
set(DRAINAGE_HEADERS
    ${INCLUDE_DIR}/TerrainModel/Drainage/WaterAnalysis.h
    ${INCLUDE_DIR}/TerrainModel/Drainage/FlowAnalysis.h
    ${INCLUDE_DIR}/TerrainModel/Drainage/CatchmentAnalysis.h
)

# 分析模块头文件
set(ANALYSIS_HEADERS
    ${INCLUDE_DIR}/TerrainModel/Analysis/VolumeCalculation.h
    ${INCLUDE_DIR}/TerrainModel/Analysis/SlopeAnalysis.h
    ${INCLUDE_DIR}/TerrainModel/Analysis/VisibilityAnalysis.h
)

# 核心源文件
set(CORE_SOURCES
    ${SRC_DIR}/Core/DTM.cpp
    ${SRC_DIR}/Core/DTMException.cpp
    ${SRC_DIR}/Core/DTMFeature.cpp
    ${SRC_DIR}/Core/DTMMesh.cpp
    ${SRC_DIR}/Core/DTMHelpers.cpp
    ${SRC_DIR}/Core/DTMDelegates.cpp
    ${SRC_DIR}/Core/DTMTinEditor.cpp
    ${SRC_DIR}/Core/DTMPond.cpp
    ${SRC_DIR}/Core/DTMFeatureEnumerator.cpp
    ${SRC_DIR}/Core/DTMMeshEnumerator.cpp
    ${SRC_DIR}/Core/DTMSideSlopeInput.cpp
    ${SRC_DIR}/Core/DTMSideSlopeInputPoint.cpp
    ${SRC_DIR}/Core/DTMDrapedLinearElement.cpp
    ${SRC_DIR}/Core/Caching.cpp
)

# 排水分析源文件
set(DRAINAGE_SOURCES
    ${SRC_DIR}/Drainage/WaterAnalysis.cpp
    ${SRC_DIR}/Drainage/FlowAnalysis.cpp
    ${SRC_DIR}/Drainage/CatchmentAnalysis.cpp
)

# 分析模块源文件
set(ANALYSIS_SOURCES
    ${SRC_DIR}/Analysis/VolumeCalculation.cpp
    ${SRC_DIR}/Analysis/SlopeAnalysis.cpp
    ${SRC_DIR}/Analysis/VisibilityAnalysis.cpp
)

# 合并所有头文件和源文件
set(ALL_HEADERS ${CORE_HEADERS} ${DRAINAGE_HEADERS} ${ANALYSIS_HEADERS})
set(ALL_SOURCES ${CORE_SOURCES} ${DRAINAGE_SOURCES} ${ANALYSIS_SOURCES})

# 创建共享库
add_library(TerrainModel SHARED ${ALL_SOURCES} ${ALL_HEADERS})

# 设置目标属性
set_target_properties(TerrainModel PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION 1
    WINDOWS_EXPORT_ALL_SYMBOLS ON
    CXX_VISIBILITY_PRESET hidden
    VISIBILITY_INLINES_HIDDEN ON
)

# 包含目录
target_include_directories(TerrainModel
    PUBLIC
        $<BUILD_INTERFACE:${INCLUDE_DIR}>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${SRC_DIR}
)

# 链接库
target_link_libraries(TerrainModel
    PUBLIC
        Threads::Threads
    PRIVATE
        # 这里可以添加其他依赖库
)

# 编译器特定的链接选项
if(MSVC)
    target_compile_definitions(TerrainModel PRIVATE
        TERRAINMODEL_EXPORT=__declspec\(dllexport\)
    )
    target_compile_definitions(TerrainModel INTERFACE
        TERRAINMODEL_EXPORT=__declspec\(dllimport\)
    )
else()
    target_compile_definitions(TerrainModel PRIVATE
        TERRAINMODEL_EXPORT=__attribute__\(\(visibility\(\"default\"\)\)\)
    )
endif()

# 安装配置
include(GNUInstallDirs)

install(TARGETS TerrainModel
    EXPORT TerrainModelTargets
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
    INCLUDES DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
)

install(DIRECTORY ${INCLUDE_DIR}/
    DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
    FILES_MATCHING PATTERN "*.h"
)

# 导出配置
install(EXPORT TerrainModelTargets
    FILE TerrainModelTargets.cmake
    NAMESPACE TerrainModel::
    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/TerrainModel
)

# 配置包配置文件
include(CMakePackageConfigHelpers)
write_basic_package_version_file(
    TerrainModelConfigVersion.cmake
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY AnyNewerVersion
)

configure_package_config_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/cmake/TerrainModelConfig.cmake.in
    ${CMAKE_CURRENT_BINARY_DIR}/TerrainModelConfig.cmake
    INSTALL_DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/TerrainModel
)

install(FILES
    ${CMAKE_CURRENT_BINARY_DIR}/TerrainModelConfig.cmake
    ${CMAKE_CURRENT_BINARY_DIR}/TerrainModelConfigVersion.cmake
    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/TerrainModel
)

# 启用测试
option(BUILD_TESTS "Build tests" ON)
if(BUILD_TESTS)
    enable_testing()
    add_subdirectory(tests)
endif()

# 启用示例
option(BUILD_EXAMPLES "Build examples" ON)
if(BUILD_EXAMPLES)
    add_subdirectory(examples)
endif()

# 生成编译数据库（用于IDE支持）
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# 调试信息
message(STATUS "TerrainModel Configuration:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  Install Prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "  Build Tests: ${BUILD_TESTS}")
message(STATUS "  Build Examples: ${BUILD_EXAMPLES}")
