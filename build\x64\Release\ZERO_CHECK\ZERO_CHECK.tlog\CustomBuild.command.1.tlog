^D:\DEV\REALITYCOREADON-CMAKE\BUILD\CMAKEFILES\F464B6B9B19A8C23424CAE80CE84DE2D\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/dev/realitycoreAdon-cmake -BD:/dev/realitycoreAdon-cmake/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/dev/realitycoreAdon-cmake/build/RealityCoreAdon.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
