/*---------------------------------------------------------------------------------------------
 * Copyright (c) Bentley Systems, Incorporated. All rights reserved.
 * See LICENSE.md in the repository root for full copyright notice.
 *--------------------------------------------------------------------------------------------*/

#pragma once

// 使用 Bentley 已有的基础设施
#include <Bentley/BeAssert.h>
#include <Bentley/bvector.h>
#include <Bentley/bmap.h>
#include <Bentley/bset.h>
#include <Bentley/WString.h>
#include <Bentley/BeNumerical.h>
#include <Bentley/Logging.h>
#include <GeomSerialization/GeomSerializationApi.h>
#include <DgnPlatform/DgnDb.h>

// 标准库
#include <memory>
#include <functional>
#include <vector>
#include <map>
#include <set>
#include <string>
#include <algorithm>
#include <cmath>
#include <limits>

// 导出宏定义 - 使用 Bentley 标准
#ifndef TERRAINMODEL_EXPORT
#if defined (__TERRAINMODEL_BUILD__)
    #define TERRAINMODEL_EXPORT __declspec(dllexport)
#else
    #define TERRAINMODEL_EXPORT __declspec(dllimport)
#endif
#endif

// 命名空间定义 - 使用 Bentley 标准
#ifndef BEGIN_BENTLEY_TERRAINMODEL_NAMESPACE
#define BEGIN_BENTLEY_TERRAINMODEL_NAMESPACE namespace Bentley { namespace TerrainModel {
#define END_BENTLEY_TERRAINMODEL_NAMESPACE }}
#endif

#define USING_NAMESPACE_BENTLEY_TERRAINMODEL using namespace Bentley::TerrainModel

BEGIN_BENTLEY_TERRAINMODEL_NAMESPACE

// 前向声明
class DTM;
class DTMFeature;
class DTMMesh;
class DTMException;

// 基础数据类型定义 - 与 TerrainModelNET 保持兼容
typedef double DTMElevation;
typedef long DTMFeatureId;
typedef long DTMUserTag;

// 使用 Bentley 几何库的点类型
using DPoint3d = ::DPoint3d;
using DPoint2d = ::DPoint2d;

// 状态码枚举
enum class TERRAINMODEL_EXPORT BentleyStatus : int
{
    Success = 0,
    Error = -1,
    InvalidArgument = -2,
    OutOfMemory = -3,
    FileNotFound = -4,
    AccessDenied = -5,
    NotImplemented = -6
};

// DTM特征类型枚举
enum class TERRAINMODEL_EXPORT DTMFeatureType : int
{
    Unknown = 0,
    Point = 1,
    PointFeature = 2,
    Breakline = 3,
    SoftBreakline = 4,
    Contour = 5,
    Void = 6,
    BreakVoid = 7,
    DrapeVoid = 8,
    Island = 9,
    Hole = 10,
    Hull = 11,
    DrapeHull = 12,
    HullLine = 13,
    VoidLine = 14,
    HoleLine = 15,
    SlopeToe = 16,
    GraphicBreak = 17,
    Region = 18,
    LowPoint = 19,
    HighPoint = 20,
    SumpLine = 21,
    RidgeLine = 22,
    DescentTrace = 23,
    AscentTrace = 24,
    Catchment = 25,
    LowPointPond = 26,
    PondIsland = 27,
    VisiblePoint = 28,
    InvisiblePoint = 29,
    VisibleLine = 30,
    InvisibleLine = 31,
    Theme = 32
};

// DTM状态枚举
enum class TERRAINMODEL_EXPORT DTMState : int
{
    Empty = 0,          // DTM为空
    Data = 1,           // DTM包含数据但未三角化
    Triangulated = 2,   // DTM已三角化
    Error = 3           // DTM处于错误状态
};

// DTM质量级别枚举
enum class TERRAINMODEL_EXPORT DTMQualityLevel : int
{
    Draft = 0,          // 草图质量
    Normal = 1,         // 正常质量
    High = 2,           // 高质量
    Ultra = 3           // 超高质量
};

// DTM插值方法枚举
enum class TERRAINMODEL_EXPORT DTMInterpolationMethod : int
{
    Linear = 0,         // 线性插值
    Cubic = 1,          // 三次插值
    Spline = 2,         // 样条插值
    Kriging = 3         // 克里金插值
};

// 可见性类型枚举
enum class TERRAINMODEL_EXPORT VisibilityType : int
{
    Visible = 0,        // 可见
    Invisible = 1,      // 不可见
    Partial = 2         // 部分可见
};

// 动态特征类型枚举
enum class TERRAINMODEL_EXPORT DTMDynamicFeatureType : int
{
    Unknown = 0,
    Boundary = 1,
    Contour = 2,
    FlowLine = 3,
    Ridge = 4,
    Valley = 5,
    Watershed = 6,
    StreamLine = 7
};

// 轮廓平滑方法枚举
enum class TERRAINMODEL_EXPORT DTMContourSmoothingMethod : int
{
    None = 0,           // 无平滑
    Linear = 1,         // 线性平滑
    Spline = 2,         // 样条平滑
    Both = 3            // 线性和样条
};

// 拖拽点代码枚举
enum class TERRAINMODEL_EXPORT DTMDrapedPointCode : int
{
    Triangle = 0,       // 点在三角形内
    Edge = 1,           // 点在边上
    Vertex = 2,         // 点在顶点上
    Outside = 3         // 点在DTM外部
};

// 库存堆特征枚举
enum class TERRAINMODEL_EXPORT StockPileFeature : int
{
    Cone = 0,           // 圆锥形
    Ridge = 1,          // 脊形
    Flat = 2            // 平顶
};

// 回调函数类型定义
using DTMProgressCallback = std::function<void(int percentComplete)>;
using DTMErrorCallback = std::function<void(const std::string& errorMessage)>;
using DTMMessageCallback = std::function<void(const std::string& message)>;

// 常量定义
constexpr int SUCCESS = 0;
constexpr int ERROR = -1;
constexpr double DTM_TOLERANCE = 1e-9;
constexpr double DTM_MAX_ELEVATION = 1e6;
constexpr double DTM_MIN_ELEVATION = -1e6;

// 版本信息
struct TERRAINMODEL_EXPORT VersionInfo
{
    static constexpr int MAJOR = 1;
    static constexpr int MINOR = 0;
    static constexpr int PATCH = 0;
    static const char* GetVersionString() { return "1.0.0"; }
    static const char* GetBuildDate() { return __DATE__; }
    static const char* GetBuildTime() { return __TIME__; }
};

END_BENTLEY_TERRAINMODEL_NAMESPACE

// 包含核心头文件
#include "TerrainModel/Core/DTMException.h"
#include "TerrainModel/Core/DTM.h"
#include "TerrainModel/Core/DTMFeature.h"
#include "TerrainModel/Core/DTMMesh.h"
#include "TerrainModel/Core/DTMHelpers.h"
#include "TerrainModel/Core/DTMDelegates.h"

// 包含分析模块
#include "TerrainModel/Drainage/WaterAnalysis.h"
#include "TerrainModel/Analysis/VolumeCalculation.h"
