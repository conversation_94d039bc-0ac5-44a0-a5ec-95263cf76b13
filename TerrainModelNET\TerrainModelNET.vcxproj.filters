﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="DTM.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DTMDrapedLinearElement.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DTMFeature.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DTMFeatureEnumerator.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DTMHelpers.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DTMMesh.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DTMPond.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DTMSideSlopeInput.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DTMSideSlopeInputPoint.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DTMTinEditor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Stdafx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Caching.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DTMMeshEnumerator.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="WaterAnalysis.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="Bentley.Civil.DTM.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DTM.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DTMDelegates.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DTMDrapedLinearElement.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DTMException.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DTMFeature.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DTMFeatureEnumerator.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DTMHelpers.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DTMMesh.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DTMSideSlopeInput.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DTMSideSlopeInputPoint.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DTMTinEditor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Stdafx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Caching.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DTMMeshEnumerator.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <None Include="TerrainModelNET.mke" />
    <None Include="TerrainModelNET.mki" />
  </ItemGroup>
</Project>