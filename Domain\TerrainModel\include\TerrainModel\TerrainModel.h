/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See LICENSE.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

// 导出宏定义
#ifndef TERRAINMODEL_EXPORT
#if defined (__TERRAINMODEL_BUILD__)
    #define TERRAINMODEL_EXPORT __declspec(dllexport)
#else
    #define TERRAINMODEL_EXPORT __declspec(dllimport)
#endif
#endif

// 命名空间定义
#ifndef BEGIN_BENTLEY_TERRAINMODEL_NAMESPACE
#define BEGIN_BENTLEY_TERRAINMODEL_NAMESPACE namespace Bentley { namespace TerrainModel {
#define END_BENTLEY_TERRAINMODEL_NAMESPACE }}
#endif

#define USING_NAMESPACE_BENTLEY_TERRAINMODEL using namespace Bentley::TerrainModel

// 兼容原有API的宏定义
#define SUCCESS 0
#define ERROR -1

// 基础数据类型
BEGIN_BENTLEY_TERRAINMODEL_NAMESPACE

typedef double DTMElevation;
typedef long DTMFeatureId;
typedef long DTMUserTag;

// 状态码 - 更新为enum class以支持命名访问
enum class BentleyStatus
{
    Success = 0,
    Failure = -1,
    SUCCESS = 0,    // 兼容代码中已使用的常量名
    ERROR = -1      // 兼容代码中已使用的常量名
};

// 添加全局常量以保持向后兼容
constexpr int SUCCESS_STATUS = 0;
constexpr int FAILURE_STATUS = -1;

/*=================================================================================**//**
* @brief 地形模型特征类型枚举
* @details 定义了地形模型中的各种特征类型
* @bsiclass
+===============+===============+===============+===============+===============+======*/
enum class DTMFeatureType
{
    Unknown = 0,             //!< 未知类型
    Point = 1,               //!< 点
    PointFeature = 2,        //!< 点特征
    Breakline = 3,           //!< 断线
    SoftBreakline = 4,       //!< 软断线
    Contour = 5,             //!< 等高线
    Void = 6,                //!< 空洞
    BreakVoid = 7,           //!< 断线空洞
    DrapeVoid = 8,           //!< 投影空洞
    Island = 9,              //!< 岛
    Hole = 10,               //!< 孔洞
    Hull = 11,               //!< 外壳
    DrapeHull = 12,          //!< 投影外壳
    HullLine = 13,           //!< 外壳线
    VoidLine = 14,           //!< 空洞线
    HoleLine = 15,           //!< 孔洞线
    SlopeToe = 16,           //!< 坡脚
    GraphicBreak = 17,       //!< 图形断线
    Region = 18,             //!< 区域
    LowPoint = 19,           //!< 低点
    HighPoint = 20,          //!< 高点
    SumpLine = 21,           //!< 汇流线
    RidgeLine = 22,          //!< 脊线
    DescentTrace = 23,       //!< 下降轨迹
    AscentTrace = 24,        //!< 上升轨迹
    Catchment = 25,          //!< 汇水区
    LowPointPond = 26,       //!< 低点水坑
    PondIsland = 27,         //!< 水坑岛
    VisiblePoint = 28,       //!< 可见点
    InvisiblePoint = 29,     //!< 不可见点
    VisibleLine = 30,        //!< 可见线
    InvisibleLine = 31,      //!< 不可见线
    Theme = 32               //!< 主题
};

END_BENTLEY_TERRAINMODEL_NAMESPACE

// 核心头文件包含
#include "TerrainModel/Core/DTMException.h"
#include "TerrainModel/Core/DTMDelegates.h"
#include "TerrainModel/Core/DTMHelpers.h"
#include "TerrainModel/Core/Caching.h"
#include "TerrainModel/Core/DTM.h"
#include "TerrainModel/Core/DTMFeature.h"
#include "TerrainModel/Core/DTMMesh.h"
#include "TerrainModel/Core/DTMPond.h"
#include "TerrainModel/Core/DTMTinEditor.h"
#include "TerrainModel/Core/DTMDrapedLinearElement.h"
#include "TerrainModel/Core/DTMFeatureEnumerator.h"
#include "TerrainModel/Core/DTMMeshEnumerator.h"
#include "TerrainModel/Core/DTMSideSlopeInput.h"
#include "TerrainModel/Core/DTMSideSlopeInputPoint.h"

// 水分析模块
#include "TerrainModel/Drainage/WaterAnalysis.h" 