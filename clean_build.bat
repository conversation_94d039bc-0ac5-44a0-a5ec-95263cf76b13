@echo off
setlocal enabledelayedexpansion

:: 脚本路径和项目根目录
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%"
cd "%PROJECT_ROOT%"

:: 清理构建目录
echo 正在清理构建目录...
if exist build (
    rd /s /q build
    if errorlevel 1 (
        echo 无法删除build目录，可能有文件被锁定
        exit /b 1
    )
)

:: 运行配置脚本
echo 重新配置项目...
call configure.bat
if errorlevel 1 (
    echo 配置失败
    exit /b 1
)

:: 运行构建脚本
echo 重新构建项目...
call build.bat %1
if errorlevel 1 (
    echo 构建失败
    exit /b 1
)

echo 清理和重建完成! 