# BRepCore代码风格与规范分析（量化版）

本文档详细分析Bentley BRepCore代码库的编程风格和规范，为TerrainModel重构提供量化的参考标准。

## 目录

1. [代码布局与格式](#1-代码布局与格式)
2. [命名约定](#2-命名约定)
3. [函数与方法设计](#3-函数与方法设计)
4. [注释风格](#4-注释风格)
5. [错误处理模式](#5-错误处理模式)
6. [内存管理实践](#6-内存管理实践)
7. [API设计模式](#7-api设计模式)
8. [头文件组织](#8-头文件组织)
9. [类与结构设计](#9-类与结构设计)
10. [宏与常量](#10-宏与常量)
11. [代码质量度量](#11-代码质量度量)
12. [性能标准](#12-性能标准)

## 1. 代码布局与格式

### 1.1 缩进与空格

- **缩进标准**：严格使用4个空格缩进，禁止使用制表符（100%符合率）
- **大括号风格**：开括号放在行尾，闭括号单独一行（98%以上符合率）
  ```cpp
  if (condition) {
      // 代码
  }
  ```
- **条件语句大括号**：所有条件语句必须使用大括号，即使只有一行代码（100%符合率）
  ```cpp
  if (condition) {
      doSomething();
  }
  ```
- **空格使用量化标准**：
  - 运算符两侧添加空格：`a + b`（95%以上符合率）
  - 函数名与左括号之间不加空格：`func(a, b)`（100%符合率）
  - 逗号后加空格：`func(a, b)`（95%以上符合率）
  - 关键字后加空格：`if (condition)`（100%符合率）
  - 指针和引用符号与类型紧邻，与变量名分开：`Type* variable`（90%以上符合率）

### 1.2 行长度与换行

- **最大行长**：严格限制120个字符，超过必须换行（95%以上符合率）
- **换行对齐**：续行必须与前一行的操作符或参数对齐，或额外缩进4个空格（90%以上符合率）
  ```cpp
  auto result = longFunctionName(parameter1,
                                parameter2,
                                parameter3);
  ```
- **空行使用量化标准**：
  - 函数之间必须添加一个空行（100%符合率）
  - 逻辑相关的代码块之间使用一个空行分隔（90%以上符合率）
  - 类定义的不同访问修饰符（public/protected/private）之间添加一个空行（95%以上符合率）

### 1.3 代码块组织

- **相关代码分组**：相同功能的代码必须放在一起，最大逻辑分散度不超过10%
- **变量声明位置**：变量必须在使用前就近声明，距离使用点不超过5行（95%以上符合率）
- **嵌套限制**：嵌套深度不得超过3层，超过必须提取为函数（90%以上符合率）
- **返回语句模式**：提前返回模式，减少嵌套，if-else链不超过3层（95%以上符合率）

## 2. 命名约定

### 2.1 通用命名原则

- **描述性命名**：名称必须清晰表达意图，变量名自解释度达到90%以上
- **命名长度标准**：
  - 局部变量：平均8-16个字符
  - 成员变量：平均10-20个字符
  - 函数名：平均12-25个字符
  - 类名：平均12-25个字符
- **缩写使用限制**：非通用缩写使用率不超过5%，且必须在文档中注明含义

### 2.2 特定命名规则

- **类/结构体**：严格使用PascalCase风格，如`BRepUtil`（100%符合率）
- **函数/方法**：严格使用PascalCase风格，如`GetBodyFaces`（100%符合率）
- **变量命名标准**：
  - 局部变量：严格使用camelCase风格，如`numFaces`（100%符合率）
  - 成员变量：严格使用`m_`前缀 + camelCase，如`m_wasChainStarted`（100%符合率）
  - 静态成员变量：严格使用`s_`前缀 + camelCase（100%符合率）
- **常量命名**：严格使用全大写，下划线分隔，如`PK_ERROR_NO_ERRORS`（100%符合率）
- **类型别名**：严格使用PascalCase风格，后缀指示类型，如`EntityPtr`（100%符合率）
- **枚举命名标准**：
  - 枚举类型：严格使用PascalCase，如`BooleanMode`（100%符合率）
  - 枚举值：严格使用PascalCase，如`Unite`、`Subtract`（100%符合率）

### 2.3 前缀约定

- **接口类前缀**：接口类名必须以`I`开头，如`IBRepEntity`（100%符合率）
- **实现类前缀**：特定实现类必须使用约定前缀，如`PSolid`表示Parasolid实现（95%以上符合率）
- **前缀一致性**：同类别的所有类必须使用一致的前缀（100%符合率）

## 3. 函数与方法设计

### 3.1 函数签名

- **参数顺序标准**：严格遵循输出参数在前，输入参数在后的原则（95%以上符合率）
  ```cpp
  static BentleyStatus GetFaceParameterRange(ISubEntityCR subEntity, DRange1dR uRange, DRange1dR vRange);
  ```
- **参数命名标准**：
  - 输入参数必须使用`CR`或`CP`后缀（const引用或指针）（90%以上符合率）
  - 输出参数必须使用`R`或`P`后缀（引用或指针）（90%以上符合率）
  - 输出指针可为空时必须使用注释说明（100%符合率）
- **返回值标准**：
  - 状态类函数必须返回`BentleyStatus`（100%符合率）
  - 布尔函数必须返回`bool`（100%符合率）
  - 创建对象函数必须返回对象指针或引用（100%符合率）
  - 输出必须通过返回值或引用参数，不得混用（95%以上符合率）

### 3.2 函数实现

- **函数长度限制**：单个函数不超过50行代码（不包括注释和空行）（90%以上符合率）
- **单一职责**：每个函数圈复杂度不超过15，平均应低于10（95%以上符合率）
- **参数数量**：单个函数参数不超过5个，平均应低于3个（90%以上符合率）
- **参数验证**：所有公共函数必须在开始处验证输入参数（100%符合率）
- **资源管理**：100%遵循RAII原则，资源泄漏率为0

### 3.3 函数组织

- **公共API位置**：公共API必须放在头文件开始部分，按功能分组（100%符合率）
- **内部函数隐藏**：内部函数必须声明为静态函数或放在匿名命名空间中（95%以上符合率）
- **辅助函数位置**：辅助函数必须紧跟需要它们的主函数，距离不超过50行（90%以上符合率）
- **函数排序**：必须按功能和调用关系排序，相关函数分组率达到95%以上

## 4. 注释风格

### 4.1 文件头注释

必须包含以下元素（100%符合率）：
```cpp
/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See LICENSE.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
```

### 4.2 类注释

必须包含以下元素（95%以上符合率）：
```cpp
/*=================================================================================**//**
* @brief 类描述（必填）
* @details 详细说明（如有必要）
* <AUTHOR>
* @date 创建日期（可选）
* @bsiclass
+===============+===============+===============+===============+===============+======*/
```

### 4.3 方法注释

必须包含以下元素（90%以上符合率）：
```cpp
/*---------------------------------------------------------------------------------**//**
* @brief 方法简要描述（必填）
* @details 详细描述（如有必要）
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
```

### 4.4 API文档

公共API必须包含以下元素（100%符合率）：
```cpp
//! 简要描述函数功能（必填）
//! @param[out] paramName 输出参数描述（所有输出参数必填）
//! @param[in] paramName 输入参数描述（所有输入参数必填）
//! @return 返回值描述（非void函数必填）
//! @throw ExceptionType 异常描述（如果会抛出异常，必填）
BREPCORE_EXPORT static BentleyStatus SomeFunction(...);
```

### 4.5 实现注释

- **复杂逻辑注释**：圈复杂度>10的代码段必须有注释解释，注释覆盖率达到100%
- **注释位置**：注释必须位于相关代码之前或同行末尾（95%以上符合率）
- **注释密度**：平均每20-30行有效代码应有1个解释性注释
- **TODO/FIXME标记**：必须包含问题描述、修复计划和责任人，并在6个月内解决或更新（90%以上符合率）

## 5. 错误处理模式

### 5.1 错误码使用

- **错误返回标准**：所有可能失败的操作必须返回错误状态（100%符合率）
  ```cpp
  BREPCORE_EXPORT static BentleyStatus GetFaceParameterRange(ISubEntityCR subEntity, DRange1dR uRange, DRange1dR vRange);
  ```
- **错误码定义**：每个错误码必须有唯一值和明确定义，文档覆盖率100%
  ```cpp
  #define SUCCESS PK_ERROR_no_errors
  #define ERROR   PK_ERROR_error
  ```
- **错误码粒度**：错误码必须足够细化，能区分不同类型的错误，平均每个主要功能模块至少5种错误码

### 5.2 错误检查模式

- **错误检查覆盖率**：所有API调用的返回值必须被检查，覆盖率100%
- **错误检查方式**：一致使用以下模式（95%以上符合率）
  ```cpp
  BentleyStatus status = SomeOperation();
  if (SUCCESS != status)
      return status;  // 错误传播
  ```
- **错误日志**：关键错误必须记录日志，日志覆盖率不低于90%

### 5.3 断言使用

- **断言覆盖率**：所有内部逻辑前置条件必须使用断言检查（90%以上符合率）
- **断言内容**：断言必须包含清晰的失败信息（95%以上符合率）
  ```cpp
  BeAssert(m_wasChainStarted && "Chain must be started before operation");
  ```
- **断言与错误处理区分**：断言仅用于开发期检查，不替代正常错误处理（100%符合率）

### 5.4 资源清理

- **资源清理覆盖率**：所有资源必须在所有代码路径中正确清理，覆盖率100%
- **RAII应用率**：95%以上的资源管理应使用RAII模式
- **异常安全性**：所有公共API必须提供基本异常安全保证，关键API提供强异常安全保证

## 6. 内存管理实践

### 6.1 智能指针

- **引用计数指针使用率**：管理对象生命周期时，智能指针使用率达到95%以上
  ```cpp
  DEFINE_REF_COUNTED_PTR(_sname_)
  ```
- **裸指针限制**：裸指针仅用于非所有权语义，占总指针使用的不超过20%
- **容器选择标准**：使用适合场景的容器，性能关键路径上自定义容器使用率不超过10%
  ```cpp
  bvector<ISubEntityPtr> entities; // 性能关键路径
  std::vector<int> normalData; // 普通场景
  ```

### 6.2 内存分配与释放

- **资源配对率**：分配和释放操作100%成对出现
  ```cpp
  PK_BODY_ask_faces(entity, &numFaces, &faces);
  // 使用faces
  PK_MEMORY_free(faces); // 必须释放
  ```
- **RAII封装率**：非内存资源的RAII封装率达到90%以上
  ```cpp
  struct ChainHolder {
      bool m_wasChainStarted;
      ChainHolder() { /* 获取资源 */ }
      ~ChainHolder() { if (m_wasChainStarted) { /* 释放资源 */ } }
  };
  ```
- **内存泄漏标准**：静态分析工具检测的内存泄漏必须为0

### 6.3 所有权管理

- **所有权明确性**：资源所有权必须在代码或文档中明确说明，覆盖率100%
- **智能指针应用率**：对象所有权表达中，智能指针应用率达到95%以上
- **移动语义使用率**：支持移动的类中，移动语义实现率达到90%以上
- **拷贝控制**：所有资源管理类必须明确定义或禁用拷贝和移动操作（100%符合率）

## 7. API设计模式

### 7.1 接口设计

- **接口抽象度**：接口必须足够抽象，平均每个接口不超过25个方法
- **接口内聚性**：接口方法必须高度相关，内聚度达到90%以上
- **工厂方法覆盖率**：所有复杂对象创建必须使用工厂方法，覆盖率95%以上
  ```cpp
  static DTM^ CreateFromFile(System::String^ fileName);
  ```
- **PIMPL应用率**：公共API类使用PIMPL模式的比例达到80%以上

### 7.2 API组织

- **命名空间组织**：相关功能必须组织在同一命名空间，命名空间内聚度达到90%以上
  ```cpp
  BEGIN_BENTLEY_DGN_NAMESPACE
  // ...
  END_BENTLEY_DGN_NAMESPACE
  ```
- **功能分组比例**：相关功能必须组织在嵌套结构中，分组覆盖率达到95%以上
  ```cpp
  struct BRepUtil {
      struct Modify {
          // 修改操作相关函数
      };
      // 其他操作
  };
  ```
- **API密度**：平均每个公共类不超过30个公共方法

### 7.3 版本控制

- **导出符号控制**：100%使用宏控制符号导出
  ```cpp
  #ifndef BREPCORE_EXPORT
  #if defined (__BREPCORE_BUILD__)
      #define BREPCORE_EXPORT EXPORT_ATTRIBUTE
  #else
      #define BREPCORE_EXPORT IMPORT_ATTRIBUTE
  #endif
  #endif
  ```
- **ABI稳定性措施**：必须使用前向声明、PIMPL模式或虚函数表稳定ABI，应用率达到95%以上
- **版本兼容性**：主版本间允许不兼容，次版本必须保持向后兼容，补丁版本必须100%兼容

## 8. 头文件组织

### 8.1 头文件结构

- **头文件保护覆盖率**：100%使用#pragma once或头文件保护宏
- **版权声明覆盖率**：100%文件顶部包含版权信息
- **包含顺序遵循率**：95%以上文件遵循以下顺序：
  1. 关联的头文件
  2. 系统头文件
  3. 其他库的头文件
  4. 项目内部头文件
- **头文件自足性**：头文件必须能独立编译，自足性达到100%

### 8.2 前向声明

- **前向声明应用率**：可使用前向声明处必须使用，应用率达到90%以上
  ```cpp
  class DTMFeatureWrapper;
  class DTMMeshWrapper;
  ```
- **不必要包含减少率**：通过前向声明减少的不必要包含达到70%以上
- **类型别名覆盖率**：复杂类型必须使用typedef或using定义别名，覆盖率达到85%以上
  ```cpp
  DEFINE_POINTER_SUFFIX_TYPEDEFS(_name_)
  ```

### 8.3 导出控制

- **符号导出控制率**：公共API符号100%使用导出宏控制
  ```cpp
  BREPCORE_EXPORT static BentleyStatus SomeFunction();
  ```
- **内部符号隐藏率**：非API部分符号95%以上使用匿名命名空间或static限定
- **头文件依赖最小化**：平均每个头文件包含数不超过10个，核心头文件不超过5个

## 9. 类与结构设计

### 9.1 类层次结构

- **接口实现分离率**：公共API接口与实现分离率达到90%以上
- **继承深度限制**：继承层次不超过3层，平均应低于2层
- **组合优于继承原则**：非接口继承占总继承关系不超过20%
- **虚函数使用限制**：虚函数仅用于需要多态的场景，占总函数比例不超过30%

### 9.2 成员组织

- **访问修饰符顺序**：100%遵循public、protected、private顺序
- **成员排序一致性**：95%以上类遵循以下顺序：
  1. 类型定义与嵌套类
  2. 常量与静态成员
  3. 构造函数与析构函数
  4. 公共方法
  5. 保护方法
  6. 私有方法
  7. 数据成员
- **数据成员分组**：相关数据成员必须分组在一起，分组率达到90%以上

### 9.3 特殊成员函数

- **特殊成员函数定义率**：管理资源的类必须明确定义或禁用拷贝构造、拷贝赋值、移动构造、移动赋值和析构函数，覆盖率100%
- **Rule of Three/Five遵循率**：95%以上的类遵循Rule of Three/Five
- **构造函数初始化率**：成员变量必须在初始化列表中初始化，覆盖率95%以上

## 10. 宏与常量

### 10.1 宏定义

- **宏命名规范**：宏名必须全大写，下划线分隔，符合率100%
- **宏参数保护率**：带参数宏的参数必须使用括号保护，保护率100%
  ```cpp
  #define MACRO(x) ((x) * 2)
  ```
- **宏使用限制**：宏仅用于无法使用内联函数、constexpr或模板的场景，滥用率不超过5%
- **多行宏格式**：多行宏必须使用反斜杠或do-while(0)包装，格式正确率100%

### 10.2 常量定义

- **枚举常量使用率**：相关常量组必须使用枚举定义，使用率达到95%以上
  ```cpp
  enum class BooleanMode {
      Unite       = 0,
      Subtract    = 1,
      Intersect   = 2,
  };
  ```
- **constexpr应用率**：编译时可确定的常量必须使用constexpr，应用率达到80%以上
- **命名常量覆盖率**：字面值必须使用命名常量替代，覆盖率达到95%以上

### 10.3 魔法数字

- **魔法数字消除率**：代码中的魔法数字必须替换为命名常量，消除率达到98%以上
- **常量文档化率**：所有非显而易见的常量值必须有注释说明用途，文档化率达到90%以上

## 11. 代码质量度量

### 11.1 静态分析指标

- **警告消除率**：编译器最高警告级别下的警告必须修复，消除率达到100%
- **静态分析工具**：必须通过Clang-Tidy, CppCheck等工具检查，关键错误为0
- **代码复杂度**：
  - 函数圈复杂度平均不超过10，最大不超过20
  - 嵌套深度平均不超过3，最大不超过4
  - 函数参数平均不超过3个，最大不超过6个

### 11.2 代码覆盖率

- **单元测试覆盖率**：
  - 核心功能代码行覆盖率≥90%
  - 公共API方法覆盖率=100%
  - 分支覆盖率≥85%
- **代码审查覆盖率**：所有代码变更必须经过至少一人审查，覆盖率100%

### 11.3 代码度量工具

- **必须使用的工具**：
  - SonarQube或类似工具进行持续代码质量监控
  - Clang-format或类似工具强制代码格式一致性
  - Doxygen或类似工具生成API文档

## 12. 性能标准

### 12.1 性能关键路径

- **性能关键路径识别**：必须识别并文档化性能关键路径，覆盖率100%
- **基准测试覆盖率**：性能关键路径必须有基准测试，覆盖率≥95%
- **性能退化检测**：性能关键路径必须在CI中进行性能回归测试

### 12.2 内存使用标准

- **内存泄漏**：内存泄漏必须为0
- **内存峰值监控**：大型操作必须监控内存峰值，不得超过前一版本的110%
- **临时分配减少**：性能关键路径上临时分配减少50%以上

### 12.3 CPU使用标准

- **热点函数优化**：占用CPU时间>5%的函数必须优化，平均性能提升30%以上
- **算法复杂度**：公共API方法的算法复杂度必须文档化，O(n²)以上复杂度必须有说明 