/*---------------------------------------------------------------------------------------------
 * Copyright (c) Bentley Systems, Incorporated. All rights reserved.
 * See LICENSE.md in the repository root for full copyright notice.
 *--------------------------------------------------------------------------------------------*/

#include "TerrainModel/Core/DTM.h"
#include "TerrainModel/Core/DTMException.h"
#include "TerrainModel/Core/DTMFeature.h"
#include <algorithm>
#include <fstream>
#include <iostream>
#include <map>
#include <set>
#include <cmath>
#include <limits>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

BEGIN_BENTLEY_TERRAINMODEL_NAMESPACE

/*=======================================================================================*//**
 * @brief DTMStandAloneFeatureInfo constructor implementation
 * @param[in] featureId Feature ID
 * @param[in] featureType Feature type
 * @param[in] userTag User tag
 * @param[in] points Point array
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
DTMStandAloneFeatureInfo::DTMStandAloneFeatureInfo(
    DTMFeatureId featureId,
    DTMFeatureType featureType,
    DTMUserTag userTag,
    const std::vector<DPoint3d>& points)
    : m_dtmFeatureId(featureId)
    , m_dtmFeatureType(featureType)
    , m_dtmUserTag(userTag)
    , m_points(points)
{
}

/*=======================================================================================*//**
 * @brief DTMDynamicFeatureInfo constructor implementation
 * @param[in] featureType Feature type
 * @param[in] points Feature point array
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
DTMDynamicFeatureInfo::DTMDynamicFeatureInfo(DTMDynamicFeatureType featureType, const std::vector<DPoint3d>& points)
    : m_featureType(featureType)
    , m_featurePoints(points)
{
}

/*=======================================================================================*//**
 * @brief PondCalculation constructor implementation
 * @param[in] calculated Whether calculation was successful
 * @param[in] elevation Pond elevation
 * @param[in] depth Pond maximum depth
 * @param[in] area Pond area
 * @param[in] volume Pond volume
 * @param[in] features Pond feature array
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
PondCalculation::PondCalculation(bool calculated, double elevation, double depth, double area, double volume,
                               std::vector<DTMDynamicFeatureInfo> features)
    : m_calculated(calculated)
    , m_elevation(elevation)
    , m_depth(depth)
    , m_area(area)
    , m_volume(volume)
    , m_pondFeatures(std::move(features))
{
}

/*=======================================================================================*//**
 * @brief VisibilityResult constructor implementation
 * @param[in] visibility Visibility type
 * @param[in] features Visibility feature array
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
VisibilityResult::VisibilityResult(VisibilityType visibility, std::vector<DTMDynamicFeatureInfo> features)
    : m_visibilityType(visibility)
    , m_features(std::move(features))
{
}

/*=======================================================================================*//**
 * @brief CutFillResult constructor implementation
 * @param[in] cutVolume Cut volume
 * @param[in] fillVolume Fill volume
 * @param[in] balanceVolume Balance volume
 * @param[in] totalArea Total area
 * @param[in] cutArea Cut area
 * @param[in] fillArea Fill area
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
CutFillResult::CutFillResult(double cutVolume, double fillVolume, double balanceVolume,
                           double totalArea, double cutArea, double fillArea)
    : m_cutVolume(cutVolume)
    , m_fillVolume(fillVolume)
    , m_balanceVolume(balanceVolume)
    , m_totalArea(totalArea)
    , m_cutArea(cutArea)
    , m_fillArea(fillArea)
{
}

/*=======================================================================================*//**
 * @brief PointCatchmentResult constructor implementation
 * @param[in] catchment Catchment tracing feature array
 * @param[in] sumpPoint Catchment point
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
PointCatchmentResult::PointCatchmentResult(std::vector<DTMDynamicFeatureInfo> catchment, const DPoint3d& sumpPoint)
    : m_catchment(std::move(catchment))
    , m_sumpPoint(sumpPoint)
{
}

/*=======================================================================================*//**
 * @brief VolumePolygon constructor implementation
 * @param[in] points Pointer to volume polygon point array
 * @param[in] numPoints Number of points
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
VolumePolygon::VolumePolygon(const DPoint3d* points, int numPoints)
{
    m_points.resize(numPoints);
    std::copy(points, points + numPoints, m_points.begin());
}

/*=======================================================================================*//**
 * @brief VolumePolygon constructor implementation
 * @param[in] points Volume polygon point array
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
VolumePolygon::VolumePolygon(const std::vector<DPoint3d>& points)
    : m_points(points)
{
}

/*=======================================================================================*//**
 * @brief VolumeResult constructor implementation (inherits from CutFillResult)
 * @param[in] cutVolume Cut volume
 * @param[in] fillVolume Fill volume
 * @param[in] balanceVolume Balance volume
 * @param[in] totalArea Total area
 * @param[in] cutArea Cut area
 * @param[in] fillArea Fill area
 * @param[in] fromArea From surface area
 * @param[in] toArea To surface area
 * @param[in] numCellsUsed Number of cells used
 * @param[in] cellArea Cell area
 * @param[in] volumePolygons Volume polygon array
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
VolumeResult::VolumeResult(double cutVolume, double fillVolume, double balanceVolume,
                         double totalArea, double cutArea, double fillArea,
                         double fromArea, double toArea, int numCellsUsed, double cellArea,
                         std::vector<VolumePolygon> volumePolygons)
    : CutFillResult(cutVolume, fillVolume, balanceVolume, totalArea, cutArea, fillArea)
    , m_fromArea(fromArea)
    , m_toArea(toArea)
    , m_numCellsUsed(numCellsUsed)
    , m_cellArea(cellArea)
    , m_volumePolygons(std::move(volumePolygons))
{
}

/*=======================================================================================*//**
 * @brief InterpolationResult constructor implementation
 * @param[in] numDtmFeatures Total number of DTM features
 * @param[in] numDtmFeaturesInterpolated Number of interpolated DTM features
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
InterpolationResult::InterpolationResult(int numDtmFeatures, int numDtmFeaturesInterpolated)
    : m_numDtmFeatures(numDtmFeatures)
    , m_numDtmFeaturesInterpolated(numDtmFeaturesInterpolated)
{
}

/*=======================================================================================*//**
 * @brief SlopeAreaResult constructor implementation
 * @param[in] area Area within specified slope range
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
SlopeAreaResult::SlopeAreaResult(double area)
    : m_area(area)
{
}

/*=======================================================================================*//**
 * @brief VolumeRange constructor implementation
 * @param[in] low Low elevation
 * @param[in] high High elevation
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
VolumeRange::VolumeRange(double low, double high)
    : m_low(low)
    , m_high(high)
{
}

/*=======================================================================================*//**
 * @brief DTMDuplicatePointError constructor implementation
 * @param[in] x X coordinate
 * @param[in] y Y coordinate
 * @param[in] z Z coordinate
 * @param[in] featureType Feature type
 * @param[in] featureId Feature ID
 * @param[in] userTag User tag
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
DTMDuplicatePointError::DTMDuplicatePointError(double x, double y, double z, DTMFeatureType featureType,
                                              DTMFeatureId featureId, DTMUserTag userTag)
    : m_x(x)
    , m_y(y)
    , m_z(z)
    , m_dtmFeatureType(featureType)
    , m_dtmFeatureId(featureId)
    , m_dtmUserTag(userTag)
{
}

/*=======================================================================================*//**
 * @brief DTMDuplicatePointError what() implementation
 * @return Error message
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
const char* DTMDuplicatePointError::what() const noexcept
{
    return "DTM duplicate point error";
}

/*=======================================================================================*//**
 * @brief DTMCrossingFeatureError constructor implementation
 * @param[in] intersectionX Intersection X coordinate
 * @param[in] intersectionY Intersection Y coordinate
 * @param[in] featureType1 First feature type
 * @param[in] featureId1 First feature ID
 * @param[in] segmentOffset1 First feature segment offset
 * @param[in] elevation1 First feature elevation
 * @param[in] distance1 First feature distance
 * @param[in] featureType2 Second feature type
 * @param[in] featureId2 Second feature ID
 * @param[in] segmentOffset2 Second feature segment offset
 * @param[in] elevation2 Second feature elevation
 * @param[in] distance2 Second feature distance
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
DTMCrossingFeatureError::DTMCrossingFeatureError(double intersectionX, double intersectionY,
                                                DTMFeatureType featureType1, DTMFeatureId featureId1, int64_t segmentOffset1,
                                                double elevation1, double distance1,
                                                DTMFeatureType featureType2, DTMFeatureId featureId2, int64_t segmentOffset2,
                                                double elevation2, double distance2)
    : m_intersectionX(intersectionX)
    , m_intersectionY(intersectionY)
    , m_dtmFeatureType1(featureType1)
    , m_dtmFeatureId1(featureId1)
    , m_segmentOffset1(segmentOffset1)
    , m_elevation1(elevation1)
    , m_distance1(distance1)
    , m_dtmFeatureType2(featureType2)
    , m_dtmFeatureId2(featureId2)
    , m_segmentOffset2(segmentOffset2)
    , m_elevation2(elevation2)
    , m_distance2(distance2)
{
}

/*=======================================================================================*//**
 * @brief DTMCrossingFeatureError what() implementation
 * @return Error message
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
const char* DTMCrossingFeatureError::what() const noexcept
{
    return "DTM crossing feature error";
}

/*=======================================================================================*//**
 * @brief DTMDrapedPoint constructor implementation
 * @param[in] elevation Point elevation
 * @param[in] slope Point slope
 * @param[in] aspect Point aspect
 * @param[in] triangle Triangle containing the point
 * @param[in] drapedType Draped point type
 * @param[in] point Point coordinates
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
DTMDrapedPoint::DTMDrapedPoint(double elevation, double slope, double aspect,
                              const std::vector<DPoint3d>& triangle, DTMDrapedPointCode drapedType,
                              const DPoint3d& point)
    : m_elevation(elevation)
    , m_slope(slope)
    , m_aspect(aspect)
    , m_triangle(triangle)
    , m_drapedType(drapedType)
    , m_point(point)
{
}

/*=======================================================================================*//**
 * @brief DTMContourOptions constructor implementation
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
DTMContourOptions::DTMContourOptions()
    : m_useOnlyContourValues(false)
    , m_zLow(0.0)
    , m_zHigh(0.0)
    , m_baseElevation(0.0)
    , m_interval(1.0)
    , m_linearSmoothingFactor(0.0)
    , m_splineSmoothingFactor(0.0)
    , m_maxSlopeOption(false)
    , m_maxSlopeValue(0.0)
    , m_highLowOption(false)
    , m_highLowContourInterval(5.0)
    , m_splineDensification(1)
    , m_smoothingOption(DTMContourSmoothingMethod::None)
{
}

/*=======================================================================================*//**
 * @brief StockPileInput constructor implementation
 * @param[in] points Stock pile boundary points
 * @param[in] type Stock pile feature type
 * @param[in] mergeOption Whether to merge with existing DTM
 * @param[in] slope Stock pile slope ratio
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
StockPileInput::StockPileInput(const std::vector<DPoint3d>& points, StockPileFeature type,
                              bool mergeOption, double slope)
    : m_stockPilePoints(points)
    , m_stockPileType(type)
    , m_mergeOption(mergeOption)
    , m_stockPileSlope(slope)
{
}

/*=======================================================================================*//**
 * @brief StockPileResult constructor implementation
 * @param[in] volume Stock pile volume
 * @param[in] stockPileDTM Stock pile DTM
 * @param[in] mergedDTM Merged DTM
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
StockPileResult::StockPileResult(double volume, std::shared_ptr<class DTM> stockPileDTM,
                                std::shared_ptr<class DTM> mergedDTM)
    : m_stockPileVolume(volume)
    , m_stockPileDTM(stockPileDTM)
    , m_mergedDTM(mergedDTM)
{
}

/*=======================================================================================*//**
 * @brief VolumeInput constructor implementation
 * @param[in] ranges Volume range table
 * @param[in] polygon Volume calculation polygon
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
VolumeInput::VolumeInput(const std::vector<VolumeRange>& ranges, const std::vector<DPoint3d>& polygon)
    : m_volumeRange(ranges)
    , m_volumePolygon(polygon)
{
}

/*=======================================================================================*//**
 * @brief DTMFileStream constructor implementation
 * @param[in] filename File path
 * @param[in] read Whether file is readable
 * @param[in] write Whether file is writable
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
DTMFileStream::DTMFileStream(const std::string& filename, bool read, bool write)
    : m_filename(filename)
    , m_canRead(read)
    , m_canWrite(write)
{
    std::ios_base::openmode mode = std::ios_base::binary;

    if (read && write)
        mode |= std::ios_base::in | std::ios_base::out;
    else if (read)
        mode |= std::ios_base::in;
    else if (write)
        mode |= std::ios_base::out;

    m_file = std::make_unique<std::fstream>(filename, mode);
}

/*=======================================================================================*//**
 * @brief DTMFileStream destructor implementation
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
DTMFileStream::~DTMFileStream()
{
    Close();
}

/*=======================================================================================*//**
 * @brief DTMFileStream Read implementation
 * @param[out] buffer Buffer to read into
 * @param[in] size Number of bytes to read
 * @return Number of bytes actually read
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
size_t DTMFileStream::Read(void* buffer, size_t size)
{
    if (!m_canRead || !m_file || !m_file->is_open())
        return 0;

    m_file->read(static_cast<char*>(buffer), size);
    return static_cast<size_t>(m_file->gcount());
}

/*=======================================================================================*//**
 * @brief DTMFileStream Write implementation
 * @param[in] buffer Buffer to write from
 * @param[in] size Number of bytes to write
 * @return Number of bytes actually written
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
size_t DTMFileStream::Write(const void* buffer, size_t size)
{
    if (!m_canWrite || !m_file || !m_file->is_open())
        return 0;

    m_file->write(static_cast<const char*>(buffer), size);
    return m_file->good() ? size : 0;
}

/*=======================================================================================*//**
 * @brief DTMFileStream Seek implementation
 * @param[in] offset Offset to seek to
 * @param[in] origin Seek origin (SEEK_SET, SEEK_CUR, SEEK_END)
 * @return True if seek was successful
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
bool DTMFileStream::Seek(int64_t offset, int origin)
{
    if (!m_file || !m_file->is_open())
        return false;

    std::ios_base::seekdir dir;
    switch (origin)
    {
    case SEEK_SET: dir = std::ios_base::beg; break;
    case SEEK_CUR: dir = std::ios_base::cur; break;
    case SEEK_END: dir = std::ios_base::end; break;
    default: return false;
    }

    m_file->seekg(offset, dir);
    m_file->seekp(offset, dir);
    return m_file->good();
}

/*=======================================================================================*//**
 * @brief DTMFileStream GetPosition implementation
 * @return Current position in stream
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
int64_t DTMFileStream::GetPosition() const
{
    if (!m_file || !m_file->is_open())
        return -1;

    return static_cast<int64_t>(m_file->tellg());
}

/*=======================================================================================*//**
 * @brief DTMFileStream GetLength implementation
 * @return Length of stream
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
int64_t DTMFileStream::GetLength() const
{
    if (!m_file || !m_file->is_open())
        return -1;

    // Save current position
    auto currentPos = m_file->tellg();

    // Seek to end to get length
    m_file->seekg(0, std::ios_base::end);
    auto length = m_file->tellg();

    // Restore position
    m_file->seekg(currentPos);

    return static_cast<int64_t>(length);
}

/*=======================================================================================*//**
 * @brief DTMFileStream Flush implementation
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
void DTMFileStream::Flush()
{
    if (m_file && m_file->is_open())
        m_file->flush();
}

/*=======================================================================================*//**
 * @brief DTMFileStream Close implementation
 * @bsimethod
+===============+===============+===============+===============+===============+======*/
void DTMFileStream::Close()
{
    if (m_file && m_file->is_open())
    {
        m_file->close();
    }
}

END_BENTLEY_TERRAINMODEL_NAMESPACE