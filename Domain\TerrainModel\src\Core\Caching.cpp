/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See LICENSE.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#include "TerrainModel/Core/Caching.h"
#include "TerrainModel/Core/DTM.h"
#include <vector>
#include <tuple>
#include <algorithm>
#include <cstring>

BEGIN_BENTLEY_TERRAINMODEL_NAMESPACE

// DTMCachedFeature 实现

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
DTMCachedFeature::DTMCachedFeature(DTMFeatureType type, DTMUserTag tag, DTMFeatureId id) :
    featureType(type),
    featureTag(tag),
    featureId(id)
{
}

// 前向声明一个辅助函数
static bool AddFeatureToCache(DTMFeatureType featureType, DTMUserTag featureTag, DTMFeatureId featureId, 
                            const std::vector<std::tuple<double, double, double>>& points, void* userData);

// DTMFeatureCacheImpl 实现类
class DTMFeatureCacheImpl
{
private:
    const DTM* m_dtm;
    size_t m_maxNumInCache;
    size_t m_numPoints;
    std::vector<DTMCachedFeature> m_features;
    void* m_userData;
    DTMBrowseFeatureCacheCallback m_callbackFunc;
    
public:
    // 构造函数
    DTMFeatureCacheImpl(const DTM& dtm, int maxNumInCache, 
                      DTMBrowseFeatureCacheCallback callbackFunc, void* userData) :
        m_dtm(&dtm),
        m_maxNumInCache(maxNumInCache),
        m_numPoints(0),
        m_callbackFunc(callbackFunc),
        m_userData(userData)
    {
        // 确保缓存大小合理
        if (m_maxNumInCache < 1000)
            m_maxNumInCache = 1000;
        else if (m_maxNumInCache > 100000)
            m_maxNumInCache = 100000;
    }
    
    // 析构函数
    ~DTMFeatureCacheImpl()
    {
        FireCallback();
    }
    
    // 清空缓存
    void EmptyCache()
    {
        m_features.clear();
        m_numPoints = 0;
    }
    
    // 触发回调
    bool FireCallback()
    {
        bool success = true;
        
        if (!m_features.empty())
        {
            success = m_callbackFunc(m_features, m_userData);
        }
        
        m_numPoints = 0;
        return success;
    }
    
    // 添加特征到缓存
    bool AddFeature(DTMFeatureType featureType, DTMUserTag featureTag, DTMFeatureId featureId, 
                  const std::vector<std::tuple<double, double, double>>& points)
    {
        // 检查是否需要先刷新缓存
        if (m_numPoints + points.size() > m_maxNumInCache)
        {
            bool success = FireCallback();
            EmptyCache();
            
            if (!success)
                return false;
        }
        
        // 创建新特征并添加到缓存
        m_features.emplace_back(featureType, featureTag, featureId);
        DTMCachedFeature& feature = m_features.back();
        
        // 复制点数据
        feature.points = points;
        
        // 更新计数器
        m_numPoints += points.size();
        
        // 如果缓存已满，刷新缓存
        if (m_numPoints > m_maxNumInCache)
        {
            bool success = FireCallback();
            EmptyCache();
            return success;
        }
        
        return true;
    }
    
    // 获取DTM
    const DTM* GetDTM() const
    {
        return m_dtm;
    }
    
    // 获取用户数据
    void* GetUserData() const
    {
        return m_userData;
    }
};

// 静态辅助函数 - 作为C风格回调的桥接
bool AddFeatureToCache(DTMFeatureType featureType, DTMUserTag featureTag, DTMFeatureId featureId, 
                     const std::vector<std::tuple<double, double, double>>& points, void* userData)
{
    DTMFeatureCache* cache = static_cast<DTMFeatureCache*>(userData);
    return cache->AddFeature(featureType, featureTag, featureId, points);
}

// DTMFeatureCache 公共方法实现

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
DTMFeatureCache::DTMFeatureCache(const DTM& dtm, int maxNumInCache, 
                               DTMBrowseFeatureCacheCallback callbackFunc, void* userData) :
    m_impl(std::make_unique<DTMFeatureCacheImpl>(dtm, maxNumInCache, callbackFunc, userData))
{
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
DTMFeatureCache::~DTMFeatureCache() = default;

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
void DTMFeatureCache::EmptyCache()
{
    m_impl->EmptyCache();
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
bool DTMFeatureCache::FireCallback()
{
    return m_impl->FireCallback();
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
bool DTMFeatureCache::AddFeature(DTMFeatureType featureType, DTMUserTag featureTag, DTMFeatureId featureId, 
                               const std::vector<std::tuple<double, double, double>>& points)
{
    return m_impl->AddFeature(featureType, featureTag, featureId, points);
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
bool DTMFeatureCache::BrowseDrainageFeatures(DTMFeatureType featureType, double& minLowPoint, 
                                          const std::vector<std::tuple<double, double>>& fencePoints)
{
    // 简单实现 - 实际应调用原生的排水特征浏览功能
    // 这里仅作为占位符实现
    
    // 创建一些测试数据
    std::vector<std::tuple<double, double, double>> testPoints;
    double testX = 0.0, testY = 0.0;
    
    // 如果有围栏点，使用第一个点作为基准
    if (!fencePoints.empty())
    {
        testX = std::get<0>(fencePoints[0]);
        testY = std::get<1>(fencePoints[0]);
    }
    
    // 创建测试点
    testPoints.push_back(std::make_tuple(testX, testY, 10.0));
    testPoints.push_back(std::make_tuple(testX + 5.0, testY, 9.0));
    testPoints.push_back(std::make_tuple(testX + 10.0, testY, 8.0));
    
    // 设置最低点
    minLowPoint = 8.0;
    
    // 添加到缓存
    return AddFeature(featureType, 1, 1, testPoints);
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
bool DTMFeatureCache::BrowseFeatures(DTMFeatureType featureType, int maxSpots)
{
    return BrowseFeatures(featureType, std::vector<std::tuple<double, double>>(), maxSpots);
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
bool DTMFeatureCache::BrowseFeatures(DTMFeatureType featureType, 
                                   const std::vector<std::tuple<double, double>>& fencePoints, 
                                   int maxSpots)
{
    // 简单实现 - 实际应调用原生的特征浏览功能
    // 这里仅作为占位符实现
    
    // 创建一些测试数据
    std::vector<std::tuple<double, double, double>> testPoints;
    double testX = 0.0, testY = 0.0;
    
    // 如果有围栏点，使用第一个点作为基准
    if (!fencePoints.empty())
    {
        testX = std::get<0>(fencePoints[0]);
        testY = std::get<1>(fencePoints[0]);
    }
    
    // 限制点数
    int numPoints = std::min(maxSpots, 10);
    
    // 创建测试点
    for (int i = 0; i < numPoints; ++i)
    {
        testPoints.push_back(std::make_tuple(testX + i * 5.0, testY + i * 5.0, 10.0 - i));
    }
    
    // 添加到缓存
    return AddFeature(featureType, 1, 1, testPoints);
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
bool DTMFeatureCache::BrowseContours(double interval, double reg, double zMin, double zMax, 
                                   bool loadRange, int smoothOption, double smoothFactor,
                                   int smoothDensity, const std::vector<std::tuple<double, double>>& fencePoints, 
                                   const std::vector<double>& contourValues, int maxSlopeOption,
                                   double maxSlopeValue, bool highLowOption)
{
    // 简单实现 - 实际应调用原生的等高线浏览功能
    // 这里仅作为占位符实现
    
    // 创建一些测试数据
    double testX = 0.0, testY = 0.0;
    
    // 如果有围栏点，使用第一个点作为基准
    if (!fencePoints.empty())
    {
        testX = std::get<0>(fencePoints[0]);
        testY = std::get<1>(fencePoints[0]);
    }
    
    // 对每个等高线值创建一个闭合曲线
    for (double z : contourValues)
    {
        // 确保高程在指定范围内
        if (z < zMin || z > zMax)
            continue;
            
        // 创建一个矩形等高线
        std::vector<std::tuple<double, double, double>> contourPoints;
        double size = 50.0 + z;
        
        contourPoints.push_back(std::make_tuple(testX - size, testY - size, z));
        contourPoints.push_back(std::make_tuple(testX + size, testY - size, z));
        contourPoints.push_back(std::make_tuple(testX + size, testY + size, z));
        contourPoints.push_back(std::make_tuple(testX - size, testY + size, z));
        contourPoints.push_back(std::make_tuple(testX - size, testY - size, z)); // 闭合
        
        // 添加到缓存
        if (!AddFeature(DTMFeatureType::ContourLine, 1, static_cast<DTMFeatureId>(z), contourPoints))
            return false;
    }
    
    return true;
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
bool DTMFeatureCache::AnalyzeElevation(const std::vector<std::tuple<double, double>>& intervals, 
                                     bool polygonized, 
                                     const std::vector<std::tuple<double, double>>& fencePoints)
{
    // 简单实现 - 实际应调用原生的高程分析功能
    // 这里仅作为占位符实现
    
    // 创建一些测试数据
    double testX = 0.0, testY = 0.0;
    
    // 如果有围栏点，使用第一个点作为基准
    if (!fencePoints.empty())
    {
        testX = std::get<0>(fencePoints[0]);
        testY = std::get<1>(fencePoints[0]);
    }
    
    // 对每个区间创建一个多边形
    for (size_t i = 0; i < intervals.size(); ++i)
    {
        // 创建一个矩形多边形
        std::vector<std::tuple<double, double, double>> polygonPoints;
        double size = 50.0 + i * 10.0;
        double z = std::get<0>(intervals[i]);
        
        polygonPoints.push_back(std::make_tuple(testX - size, testY - size, z));
        polygonPoints.push_back(std::make_tuple(testX + size, testY - size, z));
        polygonPoints.push_back(std::make_tuple(testX + size, testY + size, z));
        polygonPoints.push_back(std::make_tuple(testX - size, testY + size, z));
        polygonPoints.push_back(std::make_tuple(testX - size, testY - size, z)); // 闭合
        
        // 添加到缓存
        if (!AddFeature(DTMFeatureType::ContourLine, 1, static_cast<DTMFeatureId>(i), polygonPoints))
            return false;
    }
    
    return true;
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
bool DTMFeatureCache::AnalyzeSlope(const std::vector<std::tuple<double, double>>& intervals, 
                                 bool polygonized, 
                                 const std::vector<std::tuple<double, double>>& fencePoints)
{
    // 简单实现 - 实际应调用原生的坡度分析功能
    // 这里实现与高程分析类似
    return AnalyzeElevation(intervals, polygonized, fencePoints);
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
bool DTMFeatureCache::AnalyzeAspect(const std::vector<std::tuple<double, double>>& intervals, 
                                  bool polygonized, 
                                  const std::vector<std::tuple<double, double>>& fencePoints)
{
    // 简单实现 - 实际应调用原生的坡向分析功能
    // 这里实现与高程分析类似
    return AnalyzeElevation(intervals, polygonized, fencePoints);
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
bool DTMFeatureCache::BrowseTinPointsVisibility(double eyeX, double eyeY, double eyeZ)
{
    // 简单实现 - 实际应调用原生的TIN点可视性分析功能
    // 这里仅作为占位符实现
    
    // 创建一些测试可见点
    std::vector<std::tuple<double, double, double>> visiblePoints;
    
    for (int i = 0; i < 10; ++i)
    {
        visiblePoints.push_back(std::make_tuple(eyeX + i * 10.0, eyeY + i * 10.0, eyeZ - 5.0));
    }
    
    // 添加到缓存
    return AddFeature(DTMFeatureType::Point, 1, 1, visiblePoints);
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
bool DTMFeatureCache::BrowseTinLinesVisibility(double eyeX, double eyeY, double eyeZ)
{
    // 简单实现 - 实际应调用原生的TIN线可视性分析功能
    // 这里仅作为占位符实现
    
    // 创建一些测试可见线
    std::vector<std::tuple<double, double, double>> visibleLines;
    
    for (int i = 0; i < 5; ++i)
    {
        visibleLines.push_back(std::make_tuple(eyeX, eyeY, eyeZ - 5.0));
        visibleLines.push_back(std::make_tuple(eyeX + i * 20.0, eyeY + i * 20.0, eyeZ - 5.0));
    }
    
    // 添加到缓存
    return AddFeature(DTMFeatureType::BreakLine, 1, 1, visibleLines);
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
bool DTMFeatureCache::BrowseRadialViewSheds(double eyeX, double eyeY, double eyeZ, 
                                         int viewShedOption, int numberRadials, 
                                         double radialIncrement)
{
    // 简单实现 - 实际应调用原生的径向视域分析功能
    // 这里仅作为占位符实现
    
    // 创建视域线
    for (int i = 0; i < numberRadials; ++i)
    {
        // 计算角度
        double angle = i * radialIncrement;
        double cosAngle = std::cos(angle);
        double sinAngle = std::sin(angle);
        
        // 创建一条径向线
        std::vector<std::tuple<double, double, double>> radialLine;
        radialLine.push_back(std::make_tuple(eyeX, eyeY, eyeZ));
        
        double distance = 100.0; // 固定距离
        radialLine.push_back(std::make_tuple(
            eyeX + distance * cosAngle,
            eyeY + distance * sinAngle,
            eyeZ - 5.0));
        
        // 添加到缓存
        if (!AddFeature(DTMFeatureType::BreakLine, 1, static_cast<DTMFeatureId>(i), radialLine))
            return false;
    }
    
    return true;
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
bool DTMFeatureCache::BrowseRegionViewSheds(double eyeX, double eyeY, double eyeZ)
{
    // 简单实现 - 实际应调用原生的区域视域分析功能
    // 这里仅作为占位符实现
    
    // 创建一个视域多边形
    std::vector<std::tuple<double, double, double>> viewShedPoly;
    
    // 创建一个不规则多边形
    viewShedPoly.push_back(std::make_tuple(eyeX + 0.0, eyeY + 100.0, eyeZ - 5.0));
    viewShedPoly.push_back(std::make_tuple(eyeX + 70.0, eyeY + 70.0, eyeZ - 5.0));
    viewShedPoly.push_back(std::make_tuple(eyeX + 100.0, eyeY + 0.0, eyeZ - 5.0));
    viewShedPoly.push_back(std::make_tuple(eyeX + 70.0, eyeY - 70.0, eyeZ - 5.0));
    viewShedPoly.push_back(std::make_tuple(eyeX + 0.0, eyeY - 100.0, eyeZ - 5.0));
    viewShedPoly.push_back(std::make_tuple(eyeX - 70.0, eyeY - 70.0, eyeZ - 5.0));
    viewShedPoly.push_back(std::make_tuple(eyeX - 100.0, eyeY + 0.0, eyeZ - 5.0));
    viewShedPoly.push_back(std::make_tuple(eyeX - 70.0, eyeY + 70.0, eyeZ - 5.0));
    viewShedPoly.push_back(std::make_tuple(eyeX + 0.0, eyeY + 100.0, eyeZ - 5.0)); // 闭合
    
    // 添加到缓存
    return AddFeature(DTMFeatureType::Void, 1, 1, viewShedPoly);
}

END_BENTLEY_TERRAINMODEL_NAMESPACE
