/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See LICENSE.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModel/TerrainModel.h"
#include <functional>
#include <memory>

BEGIN_BENTLEY_TERRAINMODEL_NAMESPACE

/*=================================================================================**//**
* @brief 地形模型回调函数类型定义
* @details 定义地形模型模块中使用的各种回调函数类型
* @bsiclass
+===============+===============+===============+===============+===============+======*/

// 前向声明
class DTM;
class DTMFeature;
class DTMPoint;
class DTMTriangle;
class DTMContour;
class DTMMesh;

//! 地形模型状态回调
//! @param[in] status 状态码
//! @param[in] message 状态消息
//! @param[in] userData 用户数据
using DTMStatusCallback = std::function<void(int status, const char* message, void* userData)>;

//! 地形模型特征回调
//! @param[in] feature 特征对象
//! @param[in] userData 用户数据
//! @return 是否继续枚举
using DTMFeatureCallback = std::function<bool(const DTMFeature& feature, void* userData)>;

//! 地形模型点回调
//! @param[in] point 点对象
//! @param[in] userData 用户数据
//! @return 是否继续枚举
using DTMPointCallback = std::function<bool(const DTMPoint& point, void* userData)>;

//! 地形模型三角形回调
//! @param[in] triangle 三角形对象
//! @param[in] userData 用户数据
//! @return 是否继续枚举
using DTMTriangleCallback = std::function<bool(const DTMTriangle& triangle, void* userData)>;

//! 地形模型等高线回调
//! @param[in] contour 等高线对象
//! @param[in] userData 用户数据
//! @return 是否继续枚举
using DTMContourCallback = std::function<bool(const DTMContour& contour, void* userData)>;

//! 地形模型网格回调
//! @param[in] mesh 网格对象
//! @param[in] userData 用户数据
//! @return 是否继续枚举
using DTMMeshCallback = std::function<bool(const DTMMesh& mesh, void* userData)>;

//! 地形模型进度回调
//! @param[in] progress 进度值(0-100)
//! @param[in] message 进度消息
//! @param[in] userData 用户数据
//! @return 是否继续操作
using DTMProgressCallback = std::function<bool(int progress, const char* message, void* userData)>;

//! 地形模型转换回调
//! @param[in] pointIndex 点索引
//! @param[in,out] x X坐标
//! @param[in,out] y Y坐标
//! @param[in,out] z Z坐标
//! @param[in] userData 用户数据
using DTMTransformCallback = std::function<void(long pointIndex, double& x, double& y, double& z, void* userData)>;

END_BENTLEY_TERRAINMODEL_NAMESPACE 