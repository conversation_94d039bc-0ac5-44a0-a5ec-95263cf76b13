/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See LICENSE.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModel/TerrainModel.h"
#include "TerrainModel/Core/DTMFeature.h"
#include <vector>
#include <memory>
#include <functional>

BEGIN_BENTLEY_TERRAINMODEL_NAMESPACE

// 前向声明
class DTMDrapedLinearElementImpl;
class DTMDrapedLinearElementPointImpl;
class DTMDrapedLinearElementPointIteratorImpl;

/*=================================================================================**//**
* @brief 地形模型拖曳点代码
* @details 定义地形线性元素上拖曳点的类型代码
* @bsiclass
+===============+===============+===============+===============+===============+======*/
enum class DTMDrapedLinearElementPointCode
{
    External = 0,         //!< 外部点（不在地形上）
    Triangle = 1,         //!< 三角形面上的点
    BreakLine = 2,        //!< 断线上的点
    BreakTriangle = 3,    //!< 断三角形上的点
    Void = 4,             //!< 空洞中的点
    TrianglePoint = 5,    //!< 三角形顶点
    TriangleEdge = 6      //!< 三角形边上的点
};

/*=================================================================================**//**
* @brief 拖曳点上的特征信息
* @details 用于存储DTM拖曳点上的特征信息
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct TERRAINMODEL_EXPORT DTMDrapePointFeature
{
private:
    int m_dtmFeatureIndex;         //!< DTM特征表中的索引
    DTMFeatureType m_dtmFeatureType; //!< DTM特征类型
    DTMUserTag m_dtmUserTag;       //!< 用户标签
    DTMFeatureId m_dtmFeatureId;   //!< 特征ID
    int m_priorFeaturePoint;       //!< 拖曳点前的特征点索引
    int m_nextFeaturePoint;        //!< 拖曳点后的特征点索引
    
public:
    //! 构造函数
    //! @param[in] featureIndex 特征索引
    //! @param[in] featureType 特征类型
    //! @param[in] userTag 用户标签
    //! @param[in] featureId 特征ID
    //! @param[in] priorPoint 前一点索引
    //! @param[in] nextPoint 后一点索引
    DTMDrapePointFeature(int featureIndex, DTMFeatureType featureType, DTMUserTag userTag, 
                        DTMFeatureId featureId, int priorPoint, int nextPoint);
    
    //! 获取特征索引
    //! @return 特征索引
    int GetDtmFeatureIndex() const { return m_dtmFeatureIndex; }
    
    //! 获取特征类型
    //! @return 特征类型
    DTMFeatureType GetDtmFeatureType() const { return m_dtmFeatureType; }
    
    //! 获取特征ID
    //! @return 特征ID
    DTMFeatureId GetDtmFeatureId() const { return m_dtmFeatureId; }
    
    //! 获取用户标签
    //! @return 用户标签
    DTMUserTag GetDtmUserTag() const { return m_dtmUserTag; }
    
    //! 获取前一点索引
    //! @return 前一点索引
    int GetDtmFeaturePriorPoint() const { return m_priorFeaturePoint; }
    
    //! 获取后一点索引
    //! @return 后一点索引
    int GetDtmFeatureNextPoint() const { return m_nextFeaturePoint; }
};

/*=================================================================================**//**
* @brief 拖曳线性元素点
* @details 表示DTM线性元素上的单个拖曳点
* @bsiclass
+===============+===============+===============+===============+===============+======*/
class TERRAINMODEL_EXPORT DTMDrapedLinearElementPoint
{
private:
    DTMDrapedLinearElementPointCode m_code;              //!< 点代码
    double m_distanceAlong;                              //!< 沿线距离
    std::tuple<double, double, double> m_coordinates;    //!< 坐标
    std::vector<DTMFeatureId> m_featureIds;              //!< 特征ID列表
    std::vector<DTMDrapePointFeature> m_features;        //!< 特征列表
    
public:
    //! 构造函数
    //! @param[in] code 点代码
    //! @param[in] distance 沿线距离
    //! @param[in] coords 坐标
    //! @param[in] featureIds 特征ID列表
    //! @param[in] features 特征列表
    DTMDrapedLinearElementPoint(DTMDrapedLinearElementPointCode code, double distance, 
                              const std::tuple<double, double, double>& coords,
                              const std::vector<DTMFeatureId>& featureIds,
                              const std::vector<DTMDrapePointFeature>& features);
    
    //! 获取点代码
    //! @return 点代码
    DTMDrapedLinearElementPointCode GetCode() const { return m_code; }
    
    //! 获取沿线距离
    //! @return 沿线距离
    double GetDistanceAlong() const { return m_distanceAlong; }
    
    //! 获取点坐标
    //! @return 点坐标
    const std::tuple<double, double, double>& GetCoordinates() const { return m_coordinates; }
    
    //! 获取特征ID列表
    //! @return 特征ID列表
    const std::vector<DTMFeatureId>& GetFeatureIds() const { return m_featureIds; }
    
    //! 获取特征列表
    //! @return 特征列表
    const std::vector<DTMDrapePointFeature>& GetFeatures() const { return m_features; }
};

/*=================================================================================**//**
* @brief 拖曳线性元素点迭代器
* @details 用于遍历拖曳线性元素上的点
* @bsiclass
+===============+===============+===============+===============+===============+======*/
class TERRAINMODEL_EXPORT DTMDrapedLinearElementPointIterator
{
private:
    std::unique_ptr<DTMDrapedLinearElementPointIteratorImpl> m_impl;
    
public:
    //! 构造函数
    //! @param[in] element 拖曳线性元素
    DTMDrapedLinearElementPointIterator(const class DTMDrapedLinearElement& element);
    
    //! 析构函数
    ~DTMDrapedLinearElementPointIterator();
    
    //! 移动到下一个点
    //! @return 是否成功移动到下一点
    bool MoveNext();
    
    //! 重置迭代器
    void Reset();
    
    //! 获取当前点
    //! @return 当前点
    DTMDrapedLinearElementPoint GetCurrent() const;
    
    //! 检查是否有效
    //! @return 是否为有效迭代器
    bool IsValid() const;
};

/*=================================================================================**//**
* @brief 拖曳线性元素
* @details 表示地形上的拖曳线性元素
* @bsiclass
+===============+===============+===============+===============+===============+======*/
class TERRAINMODEL_EXPORT DTMDrapedLinearElement
{
private:
    std::unique_ptr<DTMDrapedLinearElementImpl> m_impl;
    
public:
    //! 构造函数
    DTMDrapedLinearElement();
    
    //! 析构函数
    ~DTMDrapedLinearElement();
    
    //! 创建拖曳线
    //! @param[in] dtm 地形模型
    //! @param[in] points 线上的点
    //! @return 拖曳线元素
    static std::unique_ptr<DTMDrapedLinearElement> Create(const class DTM& dtm, 
                                                        const std::vector<std::tuple<double, double>>& points);
    
    //! 获取点数量
    //! @return 点数量
    int GetPointCount() const;
    
    //! 根据索引获取点
    //! @param[in] index 点索引
    //! @return 拖曳点
    DTMDrapedLinearElementPoint GetPointByIndex(int index) const;
    
    //! 创建点迭代器
    //! @return 拖曳点迭代器
    std::unique_ptr<DTMDrapedLinearElementPointIterator> CreateIterator() const;
};

END_BENTLEY_TERRAINMODEL_NAMESPACE
