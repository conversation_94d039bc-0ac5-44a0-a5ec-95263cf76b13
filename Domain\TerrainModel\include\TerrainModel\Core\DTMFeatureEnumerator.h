/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See LICENSE.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModel/TerrainModel.h"
#include "TerrainModel/Core/DTMFeature.h"
#include <vector>
#include <memory>
#include <iterator>
#include <functional>

BEGIN_BENTLEY_TERRAINMODEL_NAMESPACE

// 前向声明
class DTM;
class DTMFeatureEnumeratorImpl;

/*=================================================================================**//**
* @brief 用户标签范围结构
* @details 用于定义用户标签的筛选范围
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct TERRAINMODEL_EXPORT DTMUserTagRange
{
    DTMUserTag low;  //!< 下限
    DTMUserTag high; //!< 上限
    
    //! 空范围常量
    static const DTMUserTagRange NullRange;
    
    //! 构造函数（单个标签）
    //! @param[in] userTag 用户标签
    DTMUserTagRange(DTMUserTag userTag);
    
    //! 构造函数（范围）
    //! @param[in] lowTag 下限标签
    //! @param[in] highTag 上限标签
    DTMUserTagRange(DTMUserTag lowTag, DTMUserTag highTag);
};

/*=================================================================================**//**
* @brief 特征枚举器类
* @details 用于遍历DTM中的特征
* @bsiclass
+===============+===============+===============+===============+===============+======*/
class TERRAINMODEL_EXPORT DTMFeatureEnumerator
{
private:
    std::unique_ptr<DTMFeatureEnumeratorImpl> m_impl;
    
public:
    //! 构造函数
    //! @param[in] dtm 地形模型
    DTMFeatureEnumerator(const DTM& dtm);
    
    //! 析构函数
    ~DTMFeatureEnumerator();
    
    //! 创建特征枚举器
    //! @param[in] dtm 地形模型
    //! @return 特征枚举器
    static std::unique_ptr<DTMFeatureEnumerator> Create(const DTM& dtm);
    
    //! 获取排序设置
    //! @return 是否排序
    bool GetSort() const;
    
    //! 设置排序设置
    //! @param[in] sort 是否排序
    void SetSort(bool sort);
    
    //! 获取是否读取源特征
    //! @return 是否读取源特征
    bool GetReadSourceFeatures() const;
    
    //! 设置是否读取源特征
    //! @param[in] read 是否读取源特征
    void SetReadSourceFeatures(bool read);
    
    //! 获取用户标签筛选范围
    //! @param[out] low 下限标签
    //! @param[out] high 上限标签
    void GetUserTagFilterRange(DTMUserTag& low, DTMUserTag& high) const;
    
    //! 设置用户标签筛选范围
    //! @param[in] low 下限标签
    //! @param[in] high 上限标签
    void SetUserTagFilterRange(DTMUserTag low, DTMUserTag high);
    
    //! 设置用户标签筛选范围
    //! @param[in] range 标签范围
    void SetUserTagFilterRange(const DTMUserTagRange& range);
    
    //! 包含所有特征类型
    void IncludeAllFeatures();
    
    //! 排除所有特征类型
    void ExcludeAllFeatures();
    
    //! 包含指定特征类型
    //! @param[in] type 特征类型
    void IncludeFeature(DTMFeatureType type);
    
    //! 排除指定特征类型
    //! @param[in] type 特征类型
    void ExcludeFeature(DTMFeatureType type);
    
    //=======================================================================================
    //! @name 迭代器支持
    //! @{
    
    //! 迭代器类型定义
    class Iterator 
    {
    private:
        DTMFeatureEnumerator* m_owner;
        int m_position;
        
    public:
        using iterator_category = std::forward_iterator_tag;
        using value_type = DTMFeature;
        using difference_type = std::ptrdiff_t;
        using pointer = DTMFeature*;
        using reference = DTMFeature&;
        
        //! 构造函数
        //! @param[in] owner 枚举器
        //! @param[in] position 位置
        Iterator(DTMFeatureEnumerator* owner, int position);
        
        //! 解引用操作符
        DTMFeature operator*() const;
        
        //! 前置递增操作符
        Iterator& operator++();
        
        //! 后置递增操作符
        Iterator operator++(int);
        
        //! 相等比较操作符
        bool operator==(const Iterator& other) const;
        
        //! 不等比较操作符
        bool operator!=(const Iterator& other) const;
    };
    
    //! 获取起始迭代器
    //! @return 起始迭代器
    Iterator begin();
    
    //! 获取结束迭代器
    //! @return 结束迭代器
    Iterator end();
    
    //! @}
    
    //! 获取特征总数
    //! @return 特征总数
    int GetCount() const;
    
    //! 获取指定位置的特征
    //! @param[in] index 索引
    //! @return 特征
    DTMFeature GetFeatureAt(int index) const;
};

END_BENTLEY_TERRAINMODEL_NAMESPACE
