/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See LICENSE.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#include "TerrainModel/Core/DTMFeatureEnumerator.h"
#include "TerrainModel/Core/DTM.h"
#include <vector>
#include <unordered_map>
#include <algorithm>

BEGIN_BENTLEY_TERRAINMODEL_NAMESPACE

// DTMUserTagRange实现

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
const DTMUserTagRange DTMUserTagRange::NullRange(std::numeric_limits<DTMUserTag>::max(), 0);

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
DTMUserTagRange::DTMUserTagRange(DTMUserTag userTag) :
    low(userTag),
    high(userTag)
{
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
DTMUserTagRange::DTMUserTagRange(DTMUserTag lowTag, DTMUserTag highTag) :
    low(lowTag),
    high(highTag)
{
}

// DTMFeatureEnumeratorImpl 实现类
class DTMFeatureEnumeratorImpl
{
private:
    std::vector<DTMFeature> m_features;
    std::unordered_map<DTMFeatureType, bool> m_includedTypes;
    bool m_sort;
    bool m_readSourceFeatures;
    DTMUserTagRange m_userTagRange;
    const DTM* m_dtm;
    
public:
    // 构造函数
    DTMFeatureEnumeratorImpl(const DTM& dtm) :
        m_dtm(&dtm),
        m_sort(false),
        m_readSourceFeatures(true),
        m_userTagRange(DTMUserTagRange::NullRange)
    {
        // 默认包含所有特征类型
        IncludeAllFeatures();
    }
    
    // 获取排序设置
    bool GetSort() const
    {
        return m_sort;
    }
    
    // 设置排序设置
    void SetSort(bool sort)
    {
        m_sort = sort;
    }
    
    // 获取是否读取源特征
    bool GetReadSourceFeatures() const
    {
        return m_readSourceFeatures;
    }
    
    // 设置是否读取源特征
    void SetReadSourceFeatures(bool read)
    {
        m_readSourceFeatures = read;
    }
    
    // 获取用户标签筛选范围
    void GetUserTagFilterRange(DTMUserTag& low, DTMUserTag& high) const
    {
        low = m_userTagRange.low;
        high = m_userTagRange.high;
    }
    
    // 设置用户标签筛选范围
    void SetUserTagFilterRange(DTMUserTag low, DTMUserTag high)
    {
        m_userTagRange.low = low;
        m_userTagRange.high = high;
    }
    
    // 包含所有特征类型
    void IncludeAllFeatures()
    {
                 // 这里我们为每种特征类型设置包含标志
         m_includedTypes[DTMFeatureType::Point] = true;
         m_includedTypes[DTMFeatureType::BreakLine] = true;
         m_includedTypes[DTMFeatureType::ContourLine] = true;
         m_includedTypes[DTMFeatureType::Void] = true;
    }
    
    // 排除所有特征类型
    void ExcludeAllFeatures()
    {
        // 清除所有包含标志
        m_includedTypes.clear();
    }
    
    // 包含指定特征类型
    void IncludeFeature(DTMFeatureType type)
    {
        m_includedTypes[type] = true;
    }
    
    // 排除指定特征类型
    void ExcludeFeature(DTMFeatureType type)
    {
        m_includedTypes[type] = false;
    }
    
    // 加载特征
    void LoadFeatures()
    {
        m_features.clear();
        
        // 这里是示例实现，实际应该从DTM获取特征
        // 创建一些测试特征数据
        for (int i = 0; i < 10; ++i)
        {
            DTMFeatureType type = (i % 3 == 0) ? DTMFeatureType::Point : 
                              (i % 3 == 1) ? DTMFeatureType::BreakLine : 
                                           DTMFeatureType::ContourLine;
                                           
            // 检查是否包含此类型
            if (m_includedTypes.count(type) == 0 || !m_includedTypes[type])
                continue;
                
            // 创建特征点
            std::vector<std::tuple<double, double, double>> points;
            for (int j = 0; j < 3; ++j)
            {
                points.push_back(std::make_tuple(i * 10.0 + j, i * 10.0 + j, i * 5.0));
            }
            
                         // 创建特征
             auto feature = (type == DTMFeatureType::Point) ?
                 DTMFeature::CreatePointFeature(
                     std::get<0>(points[0]), 
                     std::get<1>(points[0]), 
                     std::get<2>(points[0]), 
                     static_cast<DTMUserTag>(i)
                 ) :
                 DTMFeature::CreateLinearFeature(
                     points,
                     type,
                     static_cast<DTMUserTag>(i)
                 );
                 
             // 注意：这里我们使用了工厂方法创建特征，返回的是unique_ptr
             // 为了简化，我们使用拷贝构造函数创建一个临时对象
             DTMFeature tempFeature(*feature);
            
            // 检查用户标签范围
            if (m_userTagRange.low <= m_userTagRange.high)
            {
                                 DTMUserTag tag = tempFeature.GetUserTag();
                 if (tag < m_userTagRange.low || tag > m_userTagRange.high)
                     continue;
             }
             
             // 添加到集合
             m_features.push_back(tempFeature);
        }
        
                 // 如果需要排序
         if (m_sort)
         {
             std::sort(m_features.begin(), m_features.end(), 
                      [](const DTMFeature& a, const DTMFeature& b) {
                          return a.GetId() < b.GetId();
                      });
         }
    }
    
    // 获取特征总数
    int GetCount()
    {
        if (m_features.empty())
            LoadFeatures();
            
        return static_cast<int>(m_features.size());
    }
    
    // 获取指定位置的特征
    DTMFeature GetFeatureAt(int index)
    {
        if (m_features.empty())
            LoadFeatures();
            
        if (index >= 0 && index < static_cast<int>(m_features.size()))
            return m_features[index];
            
                 // 返回一个空特征作为错误处理
         return DTMFeature();
    }
};

// DTMFeatureEnumerator::Iterator 实现

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
DTMFeatureEnumerator::Iterator::Iterator(DTMFeatureEnumerator* owner, int position) :
    m_owner(owner),
    m_position(position)
{
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
DTMFeature DTMFeatureEnumerator::Iterator::operator*() const
{
    return m_owner->GetFeatureAt(m_position);
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
DTMFeatureEnumerator::Iterator& DTMFeatureEnumerator::Iterator::operator++()
{
    ++m_position;
    return *this;
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
DTMFeatureEnumerator::Iterator DTMFeatureEnumerator::Iterator::operator++(int)
{
    Iterator temp = *this;
    ++(*this);
    return temp;
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
bool DTMFeatureEnumerator::Iterator::operator==(const Iterator& other) const
{
    return m_owner == other.m_owner && m_position == other.m_position;
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
bool DTMFeatureEnumerator::Iterator::operator!=(const Iterator& other) const
{
    return !(*this == other);
}

// DTMFeatureEnumerator 实现

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
DTMFeatureEnumerator::DTMFeatureEnumerator(const DTM& dtm) :
    m_impl(std::make_unique<DTMFeatureEnumeratorImpl>(dtm))
{
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
DTMFeatureEnumerator::~DTMFeatureEnumerator() = default;

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
std::unique_ptr<DTMFeatureEnumerator> DTMFeatureEnumerator::Create(const DTM& dtm)
{
    return std::make_unique<DTMFeatureEnumerator>(dtm);
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
bool DTMFeatureEnumerator::GetSort() const
{
    return m_impl->GetSort();
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
void DTMFeatureEnumerator::SetSort(bool sort)
{
    m_impl->SetSort(sort);
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
bool DTMFeatureEnumerator::GetReadSourceFeatures() const
{
    return m_impl->GetReadSourceFeatures();
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
void DTMFeatureEnumerator::SetReadSourceFeatures(bool read)
{
    m_impl->SetReadSourceFeatures(read);
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
void DTMFeatureEnumerator::GetUserTagFilterRange(DTMUserTag& low, DTMUserTag& high) const
{
    m_impl->GetUserTagFilterRange(low, high);
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
void DTMFeatureEnumerator::SetUserTagFilterRange(DTMUserTag low, DTMUserTag high)
{
    m_impl->SetUserTagFilterRange(low, high);
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
void DTMFeatureEnumerator::SetUserTagFilterRange(const DTMUserTagRange& range)
{
    m_impl->SetUserTagFilterRange(range.low, range.high);
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
void DTMFeatureEnumerator::IncludeAllFeatures()
{
    m_impl->IncludeAllFeatures();
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
void DTMFeatureEnumerator::ExcludeAllFeatures()
{
    m_impl->ExcludeAllFeatures();
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
void DTMFeatureEnumerator::IncludeFeature(DTMFeatureType type)
{
    m_impl->IncludeFeature(type);
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
void DTMFeatureEnumerator::ExcludeFeature(DTMFeatureType type)
{
    m_impl->ExcludeFeature(type);
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
DTMFeatureEnumerator::Iterator DTMFeatureEnumerator::begin()
{
    return Iterator(this, 0);
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
DTMFeatureEnumerator::Iterator DTMFeatureEnumerator::end()
{
    return Iterator(this, GetCount());
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
int DTMFeatureEnumerator::GetCount() const
{
    return m_impl->GetCount();
}

/*---------------------------------------------------------------------------------**//**
* @bsimethod
+---------------+---------------+---------------+---------------+---------------+------*/
DTMFeature DTMFeatureEnumerator::GetFeatureAt(int index) const
{
    return m_impl->GetFeatureAt(index);
}

END_BENTLEY_TERRAINMODEL_NAMESPACE

