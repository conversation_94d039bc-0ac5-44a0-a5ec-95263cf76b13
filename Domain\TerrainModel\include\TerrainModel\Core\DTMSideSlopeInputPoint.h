/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See LICENSE.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModel/TerrainModel.h"

BEGIN_BENTLEY_TERRAINMODEL_NAMESPACE

// 此文件是重构过程中的占位符
// 将在实现阶段添加实际代码

// 前向声明
class DTMSideSlopeInputPointImpl;

/*=================================================================================**//**
* @brief 边坡输入点类
* @details 表示边坡计算的输入点，包含位置和宽度信息
* @bsiclass
+===============+===============+===============+===============+===============+======*/
class TERRAINMODEL_EXPORT DTMSideSlopeInputPoint
{
private:
    double m_x;       //!< X坐标
    double m_y;       //!< Y坐标
    double m_z;       //!< Z坐标
    double m_width;   //!< 宽度
    
public:
    //! 默认构造函数
    DTMSideSlopeInputPoint();
    
    //! 带参数构造函数
    //! @param[in] x X坐标
    //! @param[in] y Y坐标
    //! @param[in] z Z坐标
    //! @param[in] width 宽度
    DTMSideSlopeInputPoint(double x, double y, double z, double width);
    
    //! 拷贝构造函数
    DTMSideSlopeInputPoint(const DTMSideSlopeInputPoint& other);
    
    //! 移动构造函数
    DTMSideSlopeInputPoint(DTMSideSlopeInputPoint&& other) noexcept;
    
    //! 拷贝赋值运算符
    DTMSideSlopeInputPoint& operator=(const DTMSideSlopeInputPoint& other);
    
    //! 移动赋值运算符
    DTMSideSlopeInputPoint& operator=(DTMSideSlopeInputPoint&& other) noexcept;

    //! 析构函数
    ~DTMSideSlopeInputPoint();

    //=======================================================================================
    //! @name 属性访问
    //! @{
    
    //! 获取X坐标
    //! @return X坐标
    double GetX() const { return m_x; }
    
    //! 设置X坐标
    //! @param[in] x X坐标
    void SetX(double x) { m_x = x; }
    
    //! 获取Y坐标
    //! @return Y坐标
    double GetY() const { return m_y; }
    
    //! 设置Y坐标
    //! @param[in] y Y坐标
    void SetY(double y) { m_y = y; }
    
    //! 获取Z坐标
    //! @return Z坐标
    double GetZ() const { return m_z; }
    
    //! 设置Z坐标
    //! @param[in] z Z坐标
    void SetZ(double z) { m_z = z; }
    
    //! 获取宽度
    //! @return 宽度
    double GetWidth() const { return m_width; }
    
    //! 设置宽度
    //! @param[in] width 宽度
    void SetWidth(double width) { m_width = width; }
    
    //! @}
    
    //=======================================================================================
    //! @name 实用方法
    //! @{
    
    //! 计算与另一点的距离
    //! @param[in] other 另一点
    //! @return 两点间距离
    double DistanceTo(const DTMSideSlopeInputPoint& other) const;
    
    //! 计算与另一点的水平距离
    //! @param[in] other 另一点
    //! @return 两点间水平距离
    double HorizontalDistanceTo(const DTMSideSlopeInputPoint& other) const;
    
    //! 计算两点间的方位角
    //! @param[in] other 另一点
    //! @return 方位角（弧度）
    double AzimuthTo(const DTMSideSlopeInputPoint& other) const;
    
    //! @}
};

END_BENTLEY_TERRAINMODEL_NAMESPACE
