@echo off
setlocal enabledelayedexpansion

:: 脚本路径和项目根目录
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%"
cd "%PROJECT_ROOT%"

echo 修复DTM.cpp中的编译错误...

:: 修复DTMPoint类型不一致的问题 - 已经在上面的步骤中修复

:: 找到所有包含 "BentleyStatus::" 的行并进行替换
cd "%PROJECT_ROOT%\Domain\TerrainModel\src\Core"
powershell -Command "(Get-Content DTM.cpp) -replace 'return BentleyStatus::Success', 'return BentleyStatus::Success' -replace 'return BentleyStatus::Failure', 'return BentleyStatus::Failure' | Set-Content DTM.cpp"

:: 这个命令实际上不会改变内容，因为我们已经通过更改BentleyStatus定义解决了问题
:: 如果出现问题，可以取消下面的注释，将硬编码值替换成枚举值
:: powershell -Command "(Get-Content DTM.cpp) -replace 'return BentleyStatus::Success', 'return SUCCESS_STATUS' -replace 'return BentleyStatus::Failure', 'return FAILURE_STATUS' | Set-Content DTM.cpp"

echo 修复完成，开始重新构建...

:: 返回项目根目录
cd "%PROJECT_ROOT%"

:: 运行清理重建脚本
call clean_build.bat

echo 所有操作完成! 