EXPORTS 
	??_R0?AV?$_Iosb@H@std@@@8 	 DATA
	??_R0?AV?$_Ref_count_obj2@VDTMPoint@TerrainModel@BentleyM0200@@@std@@@8 	 DATA
	??_R0?AV?$basic_filebuf@DU?$char_traits@D@std@@@std@@@8 	 DATA
	??_R0?AV?$basic_ifstream@DU?$char_traits@D@std@@@std@@@8 	 DATA
	??_R0?AV?$basic_ios@DU?$char_traits@D@std@@@std@@@8 	 DATA
	??_R0?AV?$basic_istream@DU?$char_traits@D@std@@@std@@@8 	 DATA
	??_R0?AV?$basic_istringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@8 	 DATA
	??_R0?AV?$basic_ofstream@DU?$char_traits@D@std@@@std@@@8 	 DATA
	??_R0?AV?$basic_ostream@DU?$char_traits@D@std@@@std@@@8 	 DATA
	??_R0?AV?$basic_streambuf@DU?$char_traits@D@std@@@std@@@8 	 DATA
	??_R0?AV?$basic_stringbuf@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@8 	 DATA
	??_R0?AVDTMArgumentException@TerrainModel@BentleyM0200@@@8 	 DATA
	??_R0?AVDTMException@TerrainModel@BentleyM0200@@@8 	 DATA
	??_R0?AVDTMFeature@TerrainModel@BentleyM0200@@@8 	 DATA
	??_R0?AVDTMFeatureInfo@TerrainModel@BentleyM0200@@@8 	 DATA
	??_R0?AVDTMFileException@TerrainModel@BentleyM0200@@@8 	 DATA
	??_R0?AVDTMInvalidOperationException@TerrainModel@BentleyM0200@@@8 	 DATA
	??_R0?AVDTMLinearFeature@TerrainModel@BentleyM0200@@@8 	 DATA
	??_R0?AVDTMPointFeature@TerrainModel@BentleyM0200@@@8 	 DATA
	??_R0?AVDTMStandAloneFeatureInfo@TerrainModel@BentleyM0200@@@8 	 DATA
	??_R0?AV_Ref_count_base@std@@@8 	 DATA
	??_R0?AVbad_alloc@std@@@8 	 DATA
	??_R0?AVbad_array_new_length@std@@@8 	 DATA
	??_R0?AVbad_cast@std@@@8 	 DATA
	??_R0?AVexception@std@@@8 	 DATA
	??_R0?AVios_base@std@@@8 	 DATA
	??_R0?AVruntime_error@std@@@8 	 DATA
	?NullRange@DTMUserTagRange@TerrainModel@BentleyM0200@@2U123@B 	 DATA
	?_Psave@?$_Facetptr@V?$codecvt@DDU_Mbstatet@@@std@@@std@@2PEBVfacet@locale@2@EB 	 DATA
	?_Stinit@?1??_Init@?$basic_filebuf@DU?$char_traits@D@std@@@std@@IEAAXPEAU_iobuf@@W4_Initfl@23@@Z@4U_Mbstatet@@A 	 DATA
	_Avx2WmemEnabledWeakValue 	 DATA
	??$?6U?$char_traits@D@std@@@std@@YAAEAV?$basic_ostream@DU?$char_traits@D@std@@@0@AEAV10@PEBD@Z
	??$_Allocate@$0BA@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
	??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
	??$_Assign_counted_range@PEAUDPoint3d@BentleyM0200@@@?$vector@UDPoint3d@BentleyM0200@@V?$allocator@UDPoint3d@BentleyM0200@@@std@@@std@@AEAAXPEAUDPoint3d@BentleyM0200@@_K@Z
	??$_Assign_counted_range@PEAUDTMDrapePointFeature@TerrainModel@BentleyM0200@@@?$vector@UDTMDrapePointFeature@TerrainModel@BentleyM0200@@V?$allocator@UDTMDrapePointFeature@TerrainModel@BentleyM0200@@@std@@@std@@AEAAXPEAUDTMDrapePointFeature@TerrainModel@BentleyM0200@@_K@Z
	??$_Assign_counted_range@PEAV?$tuple@HHH@std@@@?$vector@V?$tuple@HHH@std@@V?$allocator@V?$tuple@HHH@std@@@2@@std@@AEAAXPEAV?$tuple@HHH@1@_K@Z
	??$_Assign_counted_range@PEAV?$tuple@NN@std@@@?$vector@V?$tuple@NN@std@@V?$allocator@V?$tuple@NN@std@@@2@@std@@AEAAXPEAV?$tuple@NN@1@_K@Z
	??$_Assign_counted_range@PEAV?$tuple@NNN@std@@@?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@AEAAXPEAV?$tuple@NNN@1@_K@Z
	??$_Assign_counted_range@PEAVDTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@@?$vector@VDTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@V?$allocator@VDTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@@std@@@std@@AEAAXPEAVDTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@_K@Z
	??$_Assign_counted_range@PEAVDTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@@?$vector@VDTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@V?$allocator@VDTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@@std@@@std@@AEAAXPEAVDTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@_K@Z
	??$_Assign_counted_range@PEAVVolumePolygon@TerrainModel@BentleyM0200@@@?$vector@VVolumePolygon@TerrainModel@BentleyM0200@@V?$allocator@VVolumePolygon@TerrainModel@BentleyM0200@@@std@@@std@@AEAAXPEAVVolumePolygon@TerrainModel@BentleyM0200@@_K@Z
	??$_Copy_memmove@PEAHPEAH@std@@YAPEAHPEAH00@Z
	??$_Copy_memmove@PEAIPEAI@std@@YAPEAIPEAI00@Z
	??$_Copy_memmove@PEANPEAN@std@@YAPEANPEAN00@Z
	??$_Copy_memmove@PEAUDPoint3d@BentleyM0200@@PEAU12@@std@@YAPEAUDPoint3d@BentleyM0200@@PEAU12@00@Z
	??$_Copy_memmove@PEAUDTMDrapePointFeature@TerrainModel@BentleyM0200@@PEAU123@@std@@YAPEAUDTMDrapePointFeature@TerrainModel@BentleyM0200@@PEAU123@00@Z
	??$_Copy_memmove@PEA_JPEA_J@std@@YAPEA_JPEA_J00@Z
	??$_Copy_memmove@PEBDPEAD@std@@YAPEADPEBD0PEAD@Z
	??$_Copy_memmove@PEBHPEAH@std@@YAPEAHPEBH0PEAH@Z
	??$_Copy_memmove@PEBUDPoint3d@BentleyM0200@@PEAU12@@std@@YAPEAUDPoint3d@BentleyM0200@@PEBU12@0PEAU12@@Z
	??$_Copy_memmove_n@PEAUDPoint3d@BentleyM0200@@PEAU12@@std@@YAPEAUDPoint3d@BentleyM0200@@PEAU12@_K0@Z
	??$_Copy_memmove_n@PEAUDTMDrapePointFeature@TerrainModel@BentleyM0200@@PEAU123@@std@@YAPEAUDTMDrapePointFeature@TerrainModel@BentleyM0200@@PEAU123@_K0@Z
	??$_Copy_memmove_n@PEA_JPEA_J@std@@YAPEA_JPEA_J_K0@Z
	??$_Copy_memmove_tail@PEAD@std@@YAPEADQEBDQEAD_K2@Z
	??$_Copy_memmove_tail@PEAH@std@@YAPEAHQEBDQEAH_K2@Z
	??$_Copy_memmove_tail@PEAI@std@@YAPEAIQEBDQEAI_K2@Z
	??$_Copy_memmove_tail@PEAN@std@@YAPEANQEBDQEAN_K2@Z
	??$_Copy_memmove_tail@PEAUDPoint3d@BentleyM0200@@@std@@YAPEAUDPoint3d@BentleyM0200@@QEBDQEAU12@_K2@Z
	??$_Copy_memmove_tail@PEAUDTMDrapePointFeature@TerrainModel@BentleyM0200@@@std@@YAPEAUDTMDrapePointFeature@TerrainModel@BentleyM0200@@QEBDQEAU123@_K2@Z
	??$_Copy_memmove_tail@PEA_J@std@@YAPEA_JQEBDQEA_J_K2@Z
	??$_Copy_vbool@V?$_Vb_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@std@@V12@@std@@YA?AV?$_Vb_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@0@V10@00@Z
	??$_Destroy_range@V?$allocator@V?$shared_ptr@VDTMPoint@TerrainModel@BentleyM0200@@@std@@@std@@@std@@YAXPEAV?$shared_ptr@VDTMPoint@TerrainModel@BentleyM0200@@@0@QEAV10@AEAV?$allocator@V?$shared_ptr@VDTMPoint@TerrainModel@BentleyM0200@@@std@@@0@@Z
	??$_Destroy_range@V?$allocator@V?$unique_ptr@UDTMTriangle@TerrainModel@BentleyM0200@@U?$default_delete@UDTMTriangle@TerrainModel@BentleyM0200@@@std@@@std@@@std@@@std@@YAXPEAV?$unique_ptr@UDTMTriangle@TerrainModel@BentleyM0200@@U?$default_delete@UDTMTriangle@TerrainModel@BentleyM0200@@@std@@@0@QEAV10@AEAV?$allocator@V?$unique_ptr@UDTMTriangle@TerrainModel@BentleyM0200@@U?$default_delete@UDTMTriangle@TerrainModel@BentleyM0200@@@std@@@std@@@0@@Z
	??$_Emplace_reallocate@AEANAEANAEANAEAN@?$vector@VDTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@V?$allocator@VDTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@@std@@@std@@AEAAPEAVDTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@QEAV234@AEAN111@Z
	??$_Emplace_reallocate@AEAW4DTMFeatureType@TerrainModel@BentleyM0200@@AEA_JAEA_J@?$vector@UDTMCachedFeature@TerrainModel@BentleyM0200@@V?$allocator@UDTMCachedFeature@TerrainModel@BentleyM0200@@@std@@@std@@AEAAPEAUDTMCachedFeature@TerrainModel@BentleyM0200@@QEAU234@AEAW4DTMFeatureType@34@AEA_J2@Z
	??$_Emplace_reallocate@AEBUDPoint3d@BentleyM0200@@@?$vector@UDPoint3d@BentleyM0200@@V?$allocator@UDPoint3d@BentleyM0200@@@std@@@std@@AEAAPEAUDPoint3d@BentleyM0200@@QEAU23@AEBU23@@Z
	??$_Emplace_reallocate@AEBV?$shared_ptr@VDTMPoint@TerrainModel@BentleyM0200@@@std@@@?$vector@V?$shared_ptr@VDTMPoint@TerrainModel@BentleyM0200@@@std@@V?$allocator@V?$shared_ptr@VDTMPoint@TerrainModel@BentleyM0200@@@std@@@2@@std@@AEAAPEAV?$shared_ptr@VDTMPoint@TerrainModel@BentleyM0200@@@1@QEAV21@AEBV21@@Z
	??$_Emplace_reallocate@AEBV?$tuple@NNN@std@@@?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@AEAAPEAV?$tuple@NNN@1@QEAV21@AEBV21@@Z
	??$_Emplace_reallocate@AEBV?$vector@HV?$allocator@H@std@@@std@@@?$vector@V?$vector@HV?$allocator@H@std@@@std@@V?$allocator@V?$vector@HV?$allocator@H@std@@@std@@@2@@std@@AEAAPEAV?$vector@HV?$allocator@H@std@@@1@QEAV21@AEBV21@@Z
	??$_Emplace_reallocate@AEBV?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@@?$vector@V?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@V?$allocator@V?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@@2@@std@@AEAAPEAV?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@1@QEAV21@AEBV21@@Z
	??$_Emplace_reallocate@AEBVDTMDrapedLinearElementPoint@TerrainModel@BentleyM0200@@@?$vector@VDTMDrapedLinearElementPoint@TerrainModel@BentleyM0200@@V?$allocator@VDTMDrapedLinearElementPoint@TerrainModel@BentleyM0200@@@std@@@std@@AEAAPEAVDTMDrapedLinearElementPoint@TerrainModel@BentleyM0200@@QEAV234@AEBV234@@Z
	??$_Emplace_reallocate@AEBVDTMFeature@TerrainModel@BentleyM0200@@@?$vector@VDTMFeature@TerrainModel@BentleyM0200@@V?$allocator@VDTMFeature@TerrainModel@BentleyM0200@@@std@@@std@@AEAAPEAVDTMFeature@TerrainModel@BentleyM0200@@QEAV234@AEBV234@@Z
	??$_Emplace_reallocate@AEBVDTMMesh@TerrainModel@BentleyM0200@@@?$vector@VDTMMesh@TerrainModel@BentleyM0200@@V?$allocator@VDTMMesh@TerrainModel@BentleyM0200@@@std@@@std@@AEAAPEAVDTMMesh@TerrainModel@BentleyM0200@@QEAV234@AEBV234@@Z
	??$_Emplace_reallocate@AEBVDTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@@?$vector@VDTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@V?$allocator@VDTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@@std@@@std@@AEAAPEAVDTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@QEAV234@AEBV234@@Z
	??$_Emplace_reallocate@V?$tuple@HHH@std@@@?$vector@V?$tuple@HHH@std@@V?$allocator@V?$tuple@HHH@std@@@2@@std@@AEAAPEAV?$tuple@HHH@1@QEAV21@$$QEAV21@@Z
	??$_Emplace_reallocate@V?$tuple@NN@std@@@?$vector@V?$tuple@NN@std@@V?$allocator@V?$tuple@NN@std@@@2@@std@@AEAAPEAV?$tuple@NN@1@QEAV21@$$QEAV21@@Z
	??$_Emplace_reallocate@V?$tuple@NNN@std@@@?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@AEAAPEAV?$tuple@NNN@1@QEAV21@$$QEAV21@@Z
	??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBU?$pair@HH@std@@V?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@2@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBU?$pair@HH@std@@V?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@2@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBU?$pair@HH@std@@V?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@2@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBU?$pair@HH@std@@V?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@2@@std@@PEAX@1@@Z
	??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CB_JV?$unique_ptr@VDTMFeatureImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeatureImpl@TerrainModel@BentleyM0200@@@std@@@std@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CB_JV?$unique_ptr@VDTMFeatureImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeatureImpl@TerrainModel@BentleyM0200@@@std@@@std@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CB_JV?$unique_ptr@VDTMFeatureImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeatureImpl@TerrainModel@BentleyM0200@@@std@@@std@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CB_JV?$unique_ptr@VDTMFeatureImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeatureImpl@TerrainModel@BentleyM0200@@@std@@@std@@@std@@PEAX@1@@Z
	??$_Fill_zero_memset@PEAI@std@@YAXPEAI_K@Z
	??$_Find_last_vectorized@$$CBDD@std@@YAPEBDQEBD0D@Z
	??$_Fnv1a_append_value@W4DTMFeatureType@TerrainModel@BentleyM0200@@@std@@YA_K_KAEBW4DTMFeatureType@TerrainModel@BentleyM0200@@@Z
	??$_Getvals@_W@?$time_get@DV?$istreambuf_iterator@DU?$char_traits@D@std@@@std@@@std@@IEAAX_WAEBV_Locinfo@1@@Z
	??$_Getvals@_W@?$time_get@_WV?$istreambuf_iterator@_WU?$char_traits@_W@std@@@std@@@std@@IEAAX_WAEBV_Locinfo@1@@Z
	??$_Hash_representation@W4DTMFeatureType@TerrainModel@BentleyM0200@@@std@@YA_KAEBW4DTMFeatureType@TerrainModel@BentleyM0200@@@Z
	??$_Is_all_bits_zero@I@std@@YA_NAEBI@Z
	??$_Make_heap_unchecked@PEAVDTMFeature@TerrainModel@BentleyM0200@@V<lambda_561f5da533b228debef7783e12f0c053>@@@std@@YAXPEAVDTMFeature@TerrainModel@BentleyM0200@@0V<lambda_561f5da533b228debef7783e12f0c053>@@@Z
	??$_Med3_unchecked@PEAVDTMFeature@TerrainModel@BentleyM0200@@V<lambda_561f5da533b228debef7783e12f0c053>@@@std@@YAXPEAVDTMFeature@TerrainModel@BentleyM0200@@00V<lambda_561f5da533b228debef7783e12f0c053>@@@Z
	??$_Partition_by_median_guess_unchecked@PEAVDTMFeature@TerrainModel@BentleyM0200@@V<lambda_561f5da533b228debef7783e12f0c053>@@@std@@YA?AU?$pair@PEAVDTMFeature@TerrainModel@BentleyM0200@@PEAV123@@0@PEAVDTMFeature@TerrainModel@BentleyM0200@@0V<lambda_561f5da533b228debef7783e12f0c053>@@@Z
	??$_Pop_heap_hole_by_index@PEAVDTMFeature@TerrainModel@BentleyM0200@@V123@V<lambda_561f5da533b228debef7783e12f0c053>@@@std@@YAXPEAVDTMFeature@TerrainModel@BentleyM0200@@_J1$$QEAV123@V<lambda_561f5da533b228debef7783e12f0c053>@@@Z
	??$_Resize@U_Value_init_tag@std@@@?$vector@UDPoint3d@BentleyM0200@@V?$allocator@UDPoint3d@BentleyM0200@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z
	??$_Resize_reallocate@I@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBI@Z
	??$_Sort_unchecked@PEAVDTMFeature@TerrainModel@BentleyM0200@@V<lambda_561f5da533b228debef7783e12f0c053>@@@std@@YAXPEAVDTMFeature@TerrainModel@BentleyM0200@@0_JV<lambda_561f5da533b228debef7783e12f0c053>@@@Z
	??$_Try_emplace@AEBW4DTMFeatureType@TerrainModel@BentleyM0200@@$$V@?$_Hash@V?$_Umap_traits@W4DTMFeatureType@TerrainModel@BentleyM0200@@_NV?$_Uhash_compare@W4DTMFeatureType@TerrainModel@BentleyM0200@@U?$hash@W4DTMFeatureType@TerrainModel@BentleyM0200@@@std@@U?$equal_to@W4DTMFeatureType@TerrainModel@BentleyM0200@@@5@@std@@V?$allocator@U?$pair@$$CBW4DTMFeatureType@TerrainModel@BentleyM0200@@_N@std@@@5@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBW4DTMFeatureType@TerrainModel@BentleyM0200@@_N@std@@PEAX@std@@_N@1@AEBW4DTMFeatureType@TerrainModel@BentleyM0200@@@Z
	??$_Try_emplace@W4DTMFeatureType@TerrainModel@BentleyM0200@@$$V@?$_Hash@V?$_Umap_traits@W4DTMFeatureType@TerrainModel@BentleyM0200@@_NV?$_Uhash_compare@W4DTMFeatureType@TerrainModel@BentleyM0200@@U?$hash@W4DTMFeatureType@TerrainModel@BentleyM0200@@@std@@U?$equal_to@W4DTMFeatureType@TerrainModel@BentleyM0200@@@5@@std@@V?$allocator@U?$pair@$$CBW4DTMFeatureType@TerrainModel@BentleyM0200@@_N@std@@@5@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBW4DTMFeatureType@TerrainModel@BentleyM0200@@_N@std@@PEAX@std@@_N@1@$$QEAW4DTMFeatureType@TerrainModel@BentleyM0200@@@Z
	??$_Uninitialized_move@PEAUDTMCachedFeature@TerrainModel@BentleyM0200@@V?$allocator@UDTMCachedFeature@TerrainModel@BentleyM0200@@@std@@@std@@YAPEAUDTMCachedFeature@TerrainModel@BentleyM0200@@QEAU123@0PEAU123@AEAV?$allocator@UDTMCachedFeature@TerrainModel@BentleyM0200@@@0@@Z
	??$_Uninitialized_move@PEAV?$shared_ptr@VDTMPoint@TerrainModel@BentleyM0200@@@std@@V?$allocator@V?$shared_ptr@VDTMPoint@TerrainModel@BentleyM0200@@@std@@@2@@std@@YAPEAV?$shared_ptr@VDTMPoint@TerrainModel@BentleyM0200@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$shared_ptr@VDTMPoint@TerrainModel@BentleyM0200@@@std@@@0@@Z
	??$_Uninitialized_move@PEAV?$vector@HV?$allocator@H@std@@@std@@V?$allocator@V?$vector@HV?$allocator@H@std@@@std@@@2@@std@@YAPEAV?$vector@HV?$allocator@H@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$vector@HV?$allocator@H@std@@@std@@@0@@Z
	??$_Uninitialized_move@PEAV?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@V?$allocator@V?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@@2@@std@@YAPEAV?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@@0@@Z
	??$_Uninitialized_move@PEAVDTMDrapedLinearElementPoint@TerrainModel@BentleyM0200@@V?$allocator@VDTMDrapedLinearElementPoint@TerrainModel@BentleyM0200@@@std@@@std@@YAPEAVDTMDrapedLinearElementPoint@TerrainModel@BentleyM0200@@QEAV123@0PEAV123@AEAV?$allocator@VDTMDrapedLinearElementPoint@TerrainModel@BentleyM0200@@@0@@Z
	??$_Uninitialized_value_construct_n@V?$allocator@UDPoint3d@BentleyM0200@@@std@@@std@@YAPEAUDPoint3d@BentleyM0200@@PEAU12@_KAEAV?$allocator@UDPoint3d@BentleyM0200@@@0@@Z
	??$construct@V?$vector@HV?$allocator@H@std@@@std@@AEBV12@@?$_Default_allocator_traits@V?$allocator@V?$vector@HV?$allocator@H@std@@@std@@@std@@@std@@SAXAEAV?$allocator@V?$vector@HV?$allocator@H@std@@@std@@@1@QEAV?$vector@HV?$allocator@H@std@@@1@AEBV31@@Z
	??$construct@V?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@AEBV12@@?$_Default_allocator_traits@V?$allocator@V?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@@std@@@std@@SAXAEAV?$allocator@V?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@@1@QEAV?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@1@AEBV31@@Z
	??$endl@DU?$char_traits@D@std@@@std@@YAAEAV?$basic_ostream@DU?$char_traits@D@std@@@0@AEAV10@@Z
	??$erase@V?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CB_JV?$unique_ptr@VDTMFeatureImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeatureImpl@TerrainModel@BentleyM0200@@@std@@@std@@@std@@@std@@@std@@@std@@$0A@@?$_Tree@V?$_Tmap_traits@_JV?$unique_ptr@VDTMFeatureImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeatureImpl@TerrainModel@BentleyM0200@@@std@@@std@@U?$less@_J@2@V?$allocator@U?$pair@$$CB_JV?$unique_ptr@VDTMFeatureImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeatureImpl@TerrainModel@BentleyM0200@@@std@@@std@@@std@@@2@$0A@@std@@@std@@QEAA?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CB_JV?$unique_ptr@VDTMFeatureImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeatureImpl@TerrainModel@BentleyM0200@@@std@@@std@@@std@@@std@@@std@@@1@V21@@Z
	??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBW4DTMFeatureType@TerrainModel@BentleyM0200@@_N@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBW4DTMFeatureType@TerrainModel@BentleyM0200@@_N@std@@@std@@@std@@@0@0AEBV10@@Z
	??$getline@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@YAAEAV?$basic_istream@DU?$char_traits@D@std@@@0@$$QEAV10@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@D@Z
	??$getline@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@YAAEAV?$basic_istream@DU?$char_traits@D@std@@@0@AEAV10@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@@Z
	??$make_unique@VDTMFeatureEnumeratorImpl@TerrainModel@BentleyM0200@@AEBVDTM@23@$0A@@std@@YA?AV?$unique_ptr@VDTMFeatureEnumeratorImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeatureEnumeratorImpl@TerrainModel@BentleyM0200@@@std@@@0@AEBVDTM@TerrainModel@BentleyM0200@@@Z
	??$make_unique@VDTMPondDesignCriteriaImpl@TerrainModel@BentleyM0200@@$$V$0A@@std@@YA?AV?$unique_ptr@VDTMPondDesignCriteriaImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMPondDesignCriteriaImpl@TerrainModel@BentleyM0200@@@std@@@0@XZ
	??$make_unique@VDTMPondDesignCriteriaImpl@TerrainModel@BentleyM0200@@AEAV123@$0A@@std@@YA?AV?$unique_ptr@VDTMPondDesignCriteriaImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMPondDesignCriteriaImpl@TerrainModel@BentleyM0200@@@std@@@0@AEAVDTMPondDesignCriteriaImpl@TerrainModel@BentleyM0200@@@Z
	??$make_unique@VDTMTinEditorImpl@TerrainModel@BentleyM0200@@AEAV123@$0A@@std@@YA?AV?$unique_ptr@VDTMTinEditorImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMTinEditorImpl@TerrainModel@BentleyM0200@@@std@@@0@AEAVDTMTinEditorImpl@TerrainModel@BentleyM0200@@@Z
	??$pow@NH$0A@@@YANNH@Z
	??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBW4DTMFeatureType@TerrainModel@BentleyM0200@@_N@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBW4DTMFeatureType@TerrainModel@BentleyM0200@@_N@std@@@std@@@std@@@0@0AEBV10@@Z
	??$use_facet@V?$codecvt@DDU_Mbstatet@@@std@@@std@@YAAEBV?$codecvt@DDU_Mbstatet@@@0@AEBVlocale@0@@Z
	??0?$basic_ofstream@DU?$char_traits@D@std@@@std@@QEAA@PEBDHH@Z
	??0?$vector@UDPoint3d@BentleyM0200@@V?$allocator@UDPoint3d@BentleyM0200@@@std@@@std@@QEAA@AEBV01@@Z
	??0?$vector@UDTMDrapePointFeature@TerrainModel@BentleyM0200@@V?$allocator@UDTMDrapePointFeature@TerrainModel@BentleyM0200@@@std@@@std@@QEAA@AEBV01@@Z
	??0?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@QEAA@AEBV01@@Z
	??0?$vector@VDTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@V?$allocator@VDTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@@std@@@std@@QEAA@AEBV01@@Z
	??0?$vector@VVolumePolygon@TerrainModel@BentleyM0200@@V?$allocator@VVolumePolygon@TerrainModel@BentleyM0200@@@std@@@std@@QEAA@AEBV01@@Z
	??0?$vector@_JV?$allocator@_J@std@@@std@@QEAA@AEBV01@@Z
	??0BrowsingCriteria@TerrainModel@BentleyM0200@@QEAA@$$QEAV012@@Z
	??0BrowsingCriteria@TerrainModel@BentleyM0200@@QEAA@AEBV012@@Z
	??0BrowsingCriteria@TerrainModel@BentleyM0200@@QEAA@XZ
	??0CutFillResult@TerrainModel@BentleyM0200@@QEAA@NNNNNN@Z
	??0DTM@TerrainModel@BentleyM0200@@QEAA@$$QEAV012@@Z
	??0DTM@TerrainModel@BentleyM0200@@QEAA@AEBV012@@Z
	??0DTM@TerrainModel@BentleyM0200@@QEAA@XZ
	??0DTMArgumentException@TerrainModel@BentleyM0200@@QEAA@$$QEAV012@@Z
	??0DTMArgumentException@TerrainModel@BentleyM0200@@QEAA@AEBV012@@Z
	??0DTMArgumentException@TerrainModel@BentleyM0200@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	??0DTMArgumentException@TerrainModel@BentleyM0200@@QEAA@PEBD@Z
	??0DTMCachedFeature@TerrainModel@BentleyM0200@@QEAA@$$QEAU012@@Z
	??0DTMCachedFeature@TerrainModel@BentleyM0200@@QEAA@AEBU012@@Z
	??0DTMCachedFeature@TerrainModel@BentleyM0200@@QEAA@W4DTMFeatureType@12@_J1@Z
	??0DTMDrapePointFeature@TerrainModel@BentleyM0200@@QEAA@HW4DTMFeatureType@12@_J1HH@Z
	??0DTMDrapedLinearElement@TerrainModel@BentleyM0200@@QEAA@XZ
	??0DTMDrapedLinearElementPoint@TerrainModel@BentleyM0200@@QEAA@$$QEAV012@@Z
	??0DTMDrapedLinearElementPoint@TerrainModel@BentleyM0200@@QEAA@AEBV012@@Z
	??0DTMDrapedLinearElementPoint@TerrainModel@BentleyM0200@@QEAA@W4DTMDrapedLinearElementPointCode@12@NAEBV?$tuple@NNN@std@@AEBV?$vector@_JV?$allocator@_J@std@@@5@AEBV?$vector@UDTMDrapePointFeature@TerrainModel@BentleyM0200@@V?$allocator@UDTMDrapePointFeature@TerrainModel@BentleyM0200@@@std@@@5@@Z
	??0DTMDrapedLinearElementPointIterator@TerrainModel@BentleyM0200@@QEAA@AEBVDTMDrapedLinearElement@12@@Z
	??0DTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@QEAA@$$QEAV012@@Z
	??0DTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@QEAA@AEBV012@@Z
	??0DTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@QEAA@W4DTMDynamicFeatureType@12@AEBV?$vector@UDPoint3d@BentleyM0200@@V?$allocator@UDPoint3d@BentleyM0200@@@std@@@std@@@Z
	??0DTMException@TerrainModel@BentleyM0200@@QEAA@AEBV012@@Z
	??0DTMException@TerrainModel@BentleyM0200@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	??0DTMException@TerrainModel@BentleyM0200@@QEAA@PEBD@Z
	??0DTMFeature@TerrainModel@BentleyM0200@@QEAA@$$QEAV012@@Z
	??0DTMFeature@TerrainModel@BentleyM0200@@QEAA@AEBV012@@Z
	??0DTMFeature@TerrainModel@BentleyM0200@@QEAA@XZ
	??0DTMFeatureCache@TerrainModel@BentleyM0200@@QEAA@AEBVDTM@12@HV?$function@$$A6A_NAEAV?$vector@UDTMCachedFeature@TerrainModel@BentleyM0200@@V?$allocator@UDTMCachedFeature@TerrainModel@BentleyM0200@@@std@@@std@@PEAX@Z@std@@PEAX@Z
	??0DTMFeatureEnumerator@TerrainModel@BentleyM0200@@QEAA@AEBVDTM@12@@Z
	??0DTMFeatureInfo@TerrainModel@BentleyM0200@@QEAA@AEBV012@@Z
	??0DTMFeatureInfo@TerrainModel@BentleyM0200@@QEAA@XZ
	??0DTMFileException@TerrainModel@BentleyM0200@@QEAA@$$QEAV012@@Z
	??0DTMFileException@TerrainModel@BentleyM0200@@QEAA@AEBV012@@Z
	??0DTMFileException@TerrainModel@BentleyM0200@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	??0DTMFileException@TerrainModel@BentleyM0200@@QEAA@PEBD@Z
	??0DTMImpl@TerrainModel@BentleyM0200@@QEAA@AEBV012@@Z
	??0DTMImpl@TerrainModel@BentleyM0200@@QEAA@XZ
	??0DTMInvalidOperationException@TerrainModel@BentleyM0200@@QEAA@$$QEAV012@@Z
	??0DTMInvalidOperationException@TerrainModel@BentleyM0200@@QEAA@AEBV012@@Z
	??0DTMInvalidOperationException@TerrainModel@BentleyM0200@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	??0DTMInvalidOperationException@TerrainModel@BentleyM0200@@QEAA@PEBD@Z
	??0DTMLinearFeature@TerrainModel@BentleyM0200@@QEAA@AEBV012@@Z
	??0DTMLinearFeature@TerrainModel@BentleyM0200@@QEAA@AEBV?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@W4DTMFeatureType@12@_J@Z
	??0DTMMesh@TerrainModel@BentleyM0200@@QEAA@$$QEAV012@@Z
	??0DTMMesh@TerrainModel@BentleyM0200@@QEAA@AEBV012@@Z
	??0DTMMesh@TerrainModel@BentleyM0200@@QEAA@XZ
	??0DTMMeshEnumerator@TerrainModel@BentleyM0200@@QEAA@AEBVDTM@12@@Z
	??0DTMMeshImpl@TerrainModel@BentleyM0200@@QEAA@AEBV012@@Z
	??0DTMPointFeature@TerrainModel@BentleyM0200@@QEAA@AEBV012@@Z
	??0DTMPointFeature@TerrainModel@BentleyM0200@@QEAA@NNN_J@Z
	??0DTMPond@TerrainModel@BentleyM0200@@QEAA@$$QEAV012@@Z
	??0DTMPond@TerrainModel@BentleyM0200@@QEAA@AEBV012@@Z
	??0DTMPond@TerrainModel@BentleyM0200@@QEAA@XZ
	??0DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEAA@$$QEAV012@@Z
	??0DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEAA@AEBV012@@Z
	??0DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEAA@W4DTMPondDesignMethod@12@AEBV?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@NNW4DTMPondTarget@12@N@Z
	??0DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEAA@W4DTMPondDesignMethod@12@AEBV?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@NNW4DTMPondTarget@12@N_NNNN33NNPEBVDTM@12@@Z
	??0DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEAA@XZ
	??0DTMSideSlopeInput@TerrainModel@BentleyM0200@@QEAA@$$QEAV012@@Z
	??0DTMSideSlopeInput@TerrainModel@BentleyM0200@@QEAA@AEBV012@@Z
	??0DTMSideSlopeInput@TerrainModel@BentleyM0200@@QEAA@XZ
	??0DTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@QEAA@$$QEAV012@@Z
	??0DTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@QEAA@AEBV012@@Z
	??0DTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@QEAA@NNNN@Z
	??0DTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@QEAA@XZ
	??0DTMStandAloneFeatureInfo@TerrainModel@BentleyM0200@@QEAA@$$QEAV012@@Z
	??0DTMStandAloneFeatureInfo@TerrainModel@BentleyM0200@@QEAA@AEBV012@@Z
	??0DTMStandAloneFeatureInfo@TerrainModel@BentleyM0200@@QEAA@_JW4DTMFeatureType@12@0AEBV?$vector@UDPoint3d@BentleyM0200@@V?$allocator@UDPoint3d@BentleyM0200@@@std@@@std@@@Z
	??0DTMTinEditor@TerrainModel@BentleyM0200@@QEAA@$$QEAV012@@Z
	??0DTMTinEditor@TerrainModel@BentleyM0200@@QEAA@AEBV012@@Z
	??0DTMTinEditor@TerrainModel@BentleyM0200@@QEAA@AEBVDTM@12@@Z
	??0DTMTinEditor@TerrainModel@BentleyM0200@@QEAA@XZ
	??0DTMUserTagRange@TerrainModel@BentleyM0200@@QEAA@_J0@Z
	??0DTMUserTagRange@TerrainModel@BentleyM0200@@QEAA@_J@Z
	??0FilterResult@TerrainModel@BentleyM0200@@QEAA@HHN@Z
	??0InterpolationResult@TerrainModel@BentleyM0200@@QEAA@HH@Z
	??0Iterator@DTMFeatureEnumerator@TerrainModel@BentleyM0200@@QEAA@PEAV123@H@Z
	??0Iterator@DTMMeshEnumerator@TerrainModel@BentleyM0200@@QEAA@PEAV123@H@Z
	??0PointCatchmentResult@TerrainModel@BentleyM0200@@QEAA@$$QEAU012@@Z
	??0PointCatchmentResult@TerrainModel@BentleyM0200@@QEAA@AEBU012@@Z
	??0PointCatchmentResult@TerrainModel@BentleyM0200@@QEAA@V?$vector@VDTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@V?$allocator@VDTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@@std@@@std@@AEBUDPoint3d@2@@Z
	??0PondAnalysisResult@TerrainModel@BentleyM0200@@QEAA@XZ
	??0PondCalculation@TerrainModel@BentleyM0200@@QEAA@$$QEAU012@@Z
	??0PondCalculation@TerrainModel@BentleyM0200@@QEAA@AEBU012@@Z
	??0PondCalculation@TerrainModel@BentleyM0200@@QEAA@_NNNNNV?$vector@VDTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@V?$allocator@VDTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@@std@@@std@@@Z
	??0SlopeAreaResult@TerrainModel@BentleyM0200@@QEAA@N@Z
	??0TileFilterCriteria@TerrainModel@BentleyM0200@@QEAA@XZ
	??0TinFilterCriteria@TerrainModel@BentleyM0200@@QEAA@XZ
	??0VisibilityResult@TerrainModel@BentleyM0200@@QEAA@$$QEAU012@@Z
	??0VisibilityResult@TerrainModel@BentleyM0200@@QEAA@AEBU012@@Z
	??0VisibilityResult@TerrainModel@BentleyM0200@@QEAA@W4VisibilityType@12@V?$vector@VDTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@V?$allocator@VDTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@@std@@@std@@@Z
	??0VolumePolygon@TerrainModel@BentleyM0200@@QEAA@$$QEAV012@@Z
	??0VolumePolygon@TerrainModel@BentleyM0200@@QEAA@AEBV012@@Z
	??0VolumePolygon@TerrainModel@BentleyM0200@@QEAA@AEBV?$vector@UDPoint3d@BentleyM0200@@V?$allocator@UDPoint3d@BentleyM0200@@@std@@@std@@@Z
	??0VolumePolygon@TerrainModel@BentleyM0200@@QEAA@PEBUDPoint3d@2@H@Z
	??0VolumeRange@TerrainModel@BentleyM0200@@QEAA@NN@Z
	??0VolumeResult@TerrainModel@BentleyM0200@@QEAA@$$QEAU012@@Z
	??0VolumeResult@TerrainModel@BentleyM0200@@QEAA@AEBU012@@Z
	??0VolumeResult@TerrainModel@BentleyM0200@@QEAA@NNNAEBV?$vector@VVolumePolygon@TerrainModel@BentleyM0200@@V?$allocator@VVolumePolygon@TerrainModel@BentleyM0200@@@std@@@std@@@Z
	??0VolumeResult@TerrainModel@BentleyM0200@@QEAA@NNNNNNAEBV?$vector@VVolumePolygon@TerrainModel@BentleyM0200@@V?$allocator@VVolumePolygon@TerrainModel@BentleyM0200@@@std@@@std@@@Z
	??0VolumeResult@TerrainModel@BentleyM0200@@QEAA@NNNNNNHNAEBV?$vector@VVolumePolygon@TerrainModel@BentleyM0200@@V?$allocator@VVolumePolygon@TerrainModel@BentleyM0200@@@std@@@std@@@Z
	??0bad_alloc@std@@QEAA@AEBV01@@Z
	??0bad_array_new_length@std@@QEAA@AEBV01@@Z
	??0bad_array_new_length@std@@QEAA@XZ
	??0bad_cast@std@@QEAA@AEBV01@@Z
	??0bad_cast@std@@QEAA@XZ
	??0exception@std@@QEAA@AEBV01@@Z
	??0runtime_error@std@@QEAA@AEBV01@@Z
	??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@U?$pair@$$CBW4DTMFeatureType@TerrainModel@BentleyM0200@@_N@std@@PEAX@std@@@std@@@std@@QEAA@XZ
	??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBU?$pair@HH@std@@V?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@2@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
	??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CB_JV?$unique_ptr@VDTMFeatureImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeatureImpl@TerrainModel@BentleyM0200@@@std@@@std@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
	??1?$_Func_class@_NAEAV?$vector@UDTMCachedFeature@TerrainModel@BentleyM0200@@V?$allocator@UDTMCachedFeature@TerrainModel@BentleyM0200@@@std@@@std@@PEAX@std@@QEAA@XZ
	??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBW4DTMFeatureType@TerrainModel@BentleyM0200@@_N@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ
	??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@$$CBW4DTMFeatureType@TerrainModel@BentleyM0200@@_N@std@@PEAX@std@@@std@@@std@@QEAA@XZ
	??1?$_Ref_count_obj2@VDTMPoint@TerrainModel@BentleyM0200@@@std@@UEAA@XZ
	??1?$_Tidy_guard@V?$vector@VDTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@V?$allocator@VDTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@@std@@@std@@@std@@QEAA@XZ
	??1?$_Tidy_guard@V?$vector@VDTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@V?$allocator@VDTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@@std@@@std@@@std@@QEAA@XZ
	??1?$_Tidy_guard@V?$vector@VVolumePolygon@TerrainModel@BentleyM0200@@V?$allocator@VVolumePolygon@TerrainModel@BentleyM0200@@@std@@@std@@@std@@QEAA@XZ
	??1?$_Uninitialized_backout_al@V?$allocator@VDTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@@std@@@std@@QEAA@XZ
	??1?$_Uninitialized_backout_al@V?$allocator@VDTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@@std@@@std@@QEAA@XZ
	??1?$_Uninitialized_backout_al@V?$allocator@VVolumePolygon@TerrainModel@BentleyM0200@@@std@@@std@@QEAA@XZ
	??1?$basic_filebuf@DU?$char_traits@D@std@@@std@@UEAA@XZ
	??1?$basic_ifstream@DU?$char_traits@D@std@@@std@@UEAA@XZ
	??1?$basic_istringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@UEAA@XZ
	??1?$basic_ofstream@DU?$char_traits@D@std@@@std@@UEAA@XZ
	??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ
	??1?$basic_stringbuf@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@UEAA@XZ
	??1?$function@$$A6A_NAEAV?$vector@UDTMCachedFeature@TerrainModel@BentleyM0200@@V?$allocator@UDTMCachedFeature@TerrainModel@BentleyM0200@@@std@@@std@@PEAX@Z@std@@QEAA@XZ
	??1?$list@U?$pair@$$CBW4DTMFeatureType@TerrainModel@BentleyM0200@@_N@std@@V?$allocator@U?$pair@$$CBW4DTMFeatureType@TerrainModel@BentleyM0200@@_N@std@@@2@@std@@QEAA@XZ
	??1?$map@U?$pair@HH@std@@V?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@2@U?$less@U?$pair@HH@std@@@2@V?$allocator@U?$pair@$$CBU?$pair@HH@std@@V?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@2@@std@@@2@@std@@QEAA@XZ
	??1?$shared_ptr@VDTMPoint@TerrainModel@BentleyM0200@@@std@@QEAA@XZ
	??1?$unique_ptr@VDTMDrapedLinearElement@TerrainModel@BentleyM0200@@U?$default_delete@VDTMDrapedLinearElement@TerrainModel@BentleyM0200@@@std@@@std@@QEAA@XZ
	??1?$unique_ptr@VDTMFeature@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeature@TerrainModel@BentleyM0200@@@std@@@std@@QEAA@XZ
	??1?$unique_ptr@VDTMFeatureCacheImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeatureCacheImpl@TerrainModel@BentleyM0200@@@std@@@std@@QEAA@XZ
	??1?$unique_ptr@VDTMFeatureEnumeratorImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeatureEnumeratorImpl@TerrainModel@BentleyM0200@@@std@@@std@@QEAA@XZ
	??1?$unique_ptr@VDTMFeatureImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeatureImpl@TerrainModel@BentleyM0200@@@std@@@std@@QEAA@XZ
	??1?$unique_ptr@VDTMPondDesignCriteriaImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMPondDesignCriteriaImpl@TerrainModel@BentleyM0200@@@std@@@std@@QEAA@XZ
	??1?$unique_ptr@V_Facet_base@std@@U?$default_delete@V_Facet_base@std@@@2@@std@@QEAA@XZ
	??1?$unordered_map@W4DTMFeatureType@TerrainModel@BentleyM0200@@_NU?$hash@W4DTMFeatureType@TerrainModel@BentleyM0200@@@std@@U?$equal_to@W4DTMFeatureType@TerrainModel@BentleyM0200@@@5@V?$allocator@U?$pair@$$CBW4DTMFeatureType@TerrainModel@BentleyM0200@@_N@std@@@5@@std@@QEAA@XZ
	??1?$vector@HV?$allocator@H@std@@@std@@QEAA@XZ
	??1?$vector@IV?$allocator@I@std@@@std@@QEAA@XZ
	??1?$vector@NV?$allocator@N@std@@@std@@QEAA@XZ
	??1?$vector@UDPoint3d@BentleyM0200@@V?$allocator@UDPoint3d@BentleyM0200@@@std@@@std@@QEAA@XZ
	??1?$vector@UDTMCachedFeature@TerrainModel@BentleyM0200@@V?$allocator@UDTMCachedFeature@TerrainModel@BentleyM0200@@@std@@@std@@QEAA@XZ
	??1?$vector@UDTMDrapePointFeature@TerrainModel@BentleyM0200@@V?$allocator@UDTMDrapePointFeature@TerrainModel@BentleyM0200@@@std@@@std@@QEAA@XZ
	??1?$vector@V?$shared_ptr@VDTMPoint@TerrainModel@BentleyM0200@@@std@@V?$allocator@V?$shared_ptr@VDTMPoint@TerrainModel@BentleyM0200@@@std@@@2@@std@@QEAA@XZ
	??1?$vector@V?$tuple@NN@std@@V?$allocator@V?$tuple@NN@std@@@2@@std@@QEAA@XZ
	??1?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@QEAA@XZ
	??1?$vector@V?$unique_ptr@UDTMTriangle@TerrainModel@BentleyM0200@@U?$default_delete@UDTMTriangle@TerrainModel@BentleyM0200@@@std@@@std@@V?$allocator@V?$unique_ptr@UDTMTriangle@TerrainModel@BentleyM0200@@U?$default_delete@UDTMTriangle@TerrainModel@BentleyM0200@@@std@@@std@@@2@@std@@QEAA@XZ
	??1?$vector@V?$vector@HV?$allocator@H@std@@@std@@V?$allocator@V?$vector@HV?$allocator@H@std@@@std@@@2@@std@@QEAA@XZ
	??1?$vector@VDTMDrapedLinearElementPoint@TerrainModel@BentleyM0200@@V?$allocator@VDTMDrapedLinearElementPoint@TerrainModel@BentleyM0200@@@std@@@std@@QEAA@XZ
	??1?$vector@VDTMFeature@TerrainModel@BentleyM0200@@V?$allocator@VDTMFeature@TerrainModel@BentleyM0200@@@std@@@std@@QEAA@XZ
	??1?$vector@VDTMMesh@TerrainModel@BentleyM0200@@V?$allocator@VDTMMesh@TerrainModel@BentleyM0200@@@std@@@std@@QEAA@XZ
	??1?$vector@_JV?$allocator@_J@std@@@std@@QEAA@XZ
	??1?$vector@_NV?$allocator@_N@std@@@std@@QEAA@XZ
	??1BrowsingCriteria@TerrainModel@BentleyM0200@@QEAA@XZ
	??1DTM@TerrainModel@BentleyM0200@@QEAA@XZ
	??1DTMArgumentException@TerrainModel@BentleyM0200@@UEAA@XZ
	??1DTMCachedFeature@TerrainModel@BentleyM0200@@QEAA@XZ
	??1DTMDrapedLinearElement@TerrainModel@BentleyM0200@@QEAA@XZ
	??1DTMDrapedLinearElementPoint@TerrainModel@BentleyM0200@@QEAA@XZ
	??1DTMDrapedLinearElementPointIterator@TerrainModel@BentleyM0200@@QEAA@XZ
	??1DTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@QEAA@XZ
	??1DTMException@TerrainModel@BentleyM0200@@UEAA@XZ
	??1DTMFeature@TerrainModel@BentleyM0200@@UEAA@XZ
	??1DTMFeatureCache@TerrainModel@BentleyM0200@@QEAA@XZ
	??1DTMFeatureEnumerator@TerrainModel@BentleyM0200@@QEAA@XZ
	??1DTMFeatureInfo@TerrainModel@BentleyM0200@@UEAA@XZ
	??1DTMFileException@TerrainModel@BentleyM0200@@UEAA@XZ
	??1DTMImpl@TerrainModel@BentleyM0200@@QEAA@XZ
	??1DTMInvalidOperationException@TerrainModel@BentleyM0200@@UEAA@XZ
	??1DTMLinearFeature@TerrainModel@BentleyM0200@@UEAA@XZ
	??1DTMMesh@TerrainModel@BentleyM0200@@QEAA@XZ
	??1DTMMeshEnumerator@TerrainModel@BentleyM0200@@QEAA@XZ
	??1DTMPointFeature@TerrainModel@BentleyM0200@@UEAA@XZ
	??1DTMPond@TerrainModel@BentleyM0200@@QEAA@XZ
	??1DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEAA@XZ
	??1DTMSideSlopeInput@TerrainModel@BentleyM0200@@QEAA@XZ
	??1DTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@QEAA@XZ
	??1DTMStandAloneFeatureInfo@TerrainModel@BentleyM0200@@UEAA@XZ
	??1DTMTinEditor@TerrainModel@BentleyM0200@@QEAA@XZ
	??1PointCatchmentResult@TerrainModel@BentleyM0200@@QEAA@XZ
	??1PondCalculation@TerrainModel@BentleyM0200@@QEAA@XZ
	??1VisibilityResult@TerrainModel@BentleyM0200@@QEAA@XZ
	??1VolumePolygon@TerrainModel@BentleyM0200@@QEAA@XZ
	??1VolumeResult@TerrainModel@BentleyM0200@@QEAA@XZ
	??1_Reallocation_guard@?$vector@V?$vector@HV?$allocator@H@std@@@std@@V?$allocator@V?$vector@HV?$allocator@H@std@@@std@@@2@@std@@QEAA@XZ
	??1_Reallocation_guard@?$vector@V?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@V?$allocator@V?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@@2@@std@@QEAA@XZ
	??1_Reallocation_guard@?$vector@VDTMDrapedLinearElementPoint@TerrainModel@BentleyM0200@@V?$allocator@VDTMDrapedLinearElementPoint@TerrainModel@BentleyM0200@@@std@@@std@@QEAA@XZ
	??1_Reallocation_guard@?$vector@VDTMFeature@TerrainModel@BentleyM0200@@V?$allocator@VDTMFeature@TerrainModel@BentleyM0200@@@std@@@std@@QEAA@XZ
	??1_Reallocation_guard@?$vector@VDTMMesh@TerrainModel@BentleyM0200@@V?$allocator@VDTMMesh@TerrainModel@BentleyM0200@@@std@@@std@@QEAA@XZ
	??1_Reallocation_guard@?$vector@VDTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@V?$allocator@VDTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@@std@@@std@@QEAA@XZ
	??1_Ref_count_base@std@@UEAA@XZ
	??1_Sentry_base@?$basic_istream@DU?$char_traits@D@std@@@std@@QEAA@XZ
	??1_Sentry_base@?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAA@XZ
	??1bad_array_new_length@std@@UEAA@XZ
	??1bad_cast@std@@UEAA@XZ
	??1exception@std@@UEAA@XZ
	??1locale@std@@QEAA@XZ
	??1sentry@?$basic_istream@DU?$char_traits@D@std@@@std@@QEAA@XZ
	??1sentry@?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAA@XZ
	??4?$vector@UDPoint3d@BentleyM0200@@V?$allocator@UDPoint3d@BentleyM0200@@@std@@@std@@QEAAAEAV01@$$QEAV01@@Z
	??4?$vector@_JV?$allocator@_J@std@@@std@@QEAAAEAV01@AEBV01@@Z
	??4BrowsingCriteria@TerrainModel@BentleyM0200@@QEAAAEAV012@$$QEAV012@@Z
	??4BrowsingCriteria@TerrainModel@BentleyM0200@@QEAAAEAV012@AEBV012@@Z
	??4CutFillResult@TerrainModel@BentleyM0200@@QEAAAEAU012@$$QEAU012@@Z
	??4CutFillResult@TerrainModel@BentleyM0200@@QEAAAEAU012@AEBU012@@Z
	??4DTM@TerrainModel@BentleyM0200@@QEAAAEAV012@$$QEAV012@@Z
	??4DTM@TerrainModel@BentleyM0200@@QEAAAEAV012@AEBV012@@Z
	??4DTMArgumentException@TerrainModel@BentleyM0200@@QEAAAEAV012@$$QEAV012@@Z
	??4DTMArgumentException@TerrainModel@BentleyM0200@@QEAAAEAV012@AEBV012@@Z
	??4DTMCachedFeature@TerrainModel@BentleyM0200@@QEAAAEAU012@$$QEAU012@@Z
	??4DTMCachedFeature@TerrainModel@BentleyM0200@@QEAAAEAU012@AEBU012@@Z
	??4DTMDrapePointFeature@TerrainModel@BentleyM0200@@QEAAAEAU012@$$QEAU012@@Z
	??4DTMDrapePointFeature@TerrainModel@BentleyM0200@@QEAAAEAU012@AEBU012@@Z
	??4DTMDrapedLinearElementPoint@TerrainModel@BentleyM0200@@QEAAAEAV012@$$QEAV012@@Z
	??4DTMDrapedLinearElementPoint@TerrainModel@BentleyM0200@@QEAAAEAV012@AEBV012@@Z
	??4DTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@QEAAAEAV012@$$QEAV012@@Z
	??4DTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@QEAAAEAV012@AEBV012@@Z
	??4DTMException@TerrainModel@BentleyM0200@@QEAAAEAV012@AEBV012@@Z
	??4DTMFeature@TerrainModel@BentleyM0200@@QEAAAEAV012@$$QEAV012@@Z
	??4DTMFeature@TerrainModel@BentleyM0200@@QEAAAEAV012@AEBV012@@Z
	??4DTMFeatureInfo@TerrainModel@BentleyM0200@@QEAAAEAV012@AEBV012@@Z
	??4DTMFileException@TerrainModel@BentleyM0200@@QEAAAEAV012@$$QEAV012@@Z
	??4DTMFileException@TerrainModel@BentleyM0200@@QEAAAEAV012@AEBV012@@Z
	??4DTMHelpers@TerrainModel@BentleyM0200@@QEAAAEAV012@$$QEAV012@@Z
	??4DTMHelpers@TerrainModel@BentleyM0200@@QEAAAEAV012@AEBV012@@Z
	??4DTMImpl@TerrainModel@BentleyM0200@@QEAAAEAV012@AEBV012@@Z
	??4DTMInvalidOperationException@TerrainModel@BentleyM0200@@QEAAAEAV012@$$QEAV012@@Z
	??4DTMInvalidOperationException@TerrainModel@BentleyM0200@@QEAAAEAV012@AEBV012@@Z
	??4DTMLinearFeature@TerrainModel@BentleyM0200@@QEAAAEAV012@AEBV012@@Z
	??4DTMMesh@TerrainModel@BentleyM0200@@QEAAAEAV012@$$QEAV012@@Z
	??4DTMMesh@TerrainModel@BentleyM0200@@QEAAAEAV012@AEBV012@@Z
	??4DTMPointFeature@TerrainModel@BentleyM0200@@QEAAAEAV012@AEBV012@@Z
	??4DTMPond@TerrainModel@BentleyM0200@@QEAAAEAV012@$$QEAV012@@Z
	??4DTMPond@TerrainModel@BentleyM0200@@QEAAAEAV012@AEBV012@@Z
	??4DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEAAAEAV012@$$QEAV012@@Z
	??4DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEAAAEAV012@AEBV012@@Z
	??4DTMSideSlopeInput@TerrainModel@BentleyM0200@@QEAAAEAV012@$$QEAV012@@Z
	??4DTMSideSlopeInput@TerrainModel@BentleyM0200@@QEAAAEAV012@AEBV012@@Z
	??4DTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@QEAAAEAV012@$$QEAV012@@Z
	??4DTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@QEAAAEAV012@AEBV012@@Z
	??4DTMStandAloneFeatureInfo@TerrainModel@BentleyM0200@@QEAAAEAV012@$$QEAV012@@Z
	??4DTMStandAloneFeatureInfo@TerrainModel@BentleyM0200@@QEAAAEAV012@AEBV012@@Z
	??4DTMTinEditor@TerrainModel@BentleyM0200@@QEAAAEAV012@$$QEAV012@@Z
	??4DTMTinEditor@TerrainModel@BentleyM0200@@QEAAAEAV012@AEBV012@@Z
	??4DTMUserTagRange@TerrainModel@BentleyM0200@@QEAAAEAU012@$$QEAU012@@Z
	??4DTMUserTagRange@TerrainModel@BentleyM0200@@QEAAAEAU012@AEBU012@@Z
	??4FilterResult@TerrainModel@BentleyM0200@@QEAAAEAV012@$$QEAV012@@Z
	??4FilterResult@TerrainModel@BentleyM0200@@QEAAAEAV012@AEBV012@@Z
	??4InterpolationResult@TerrainModel@BentleyM0200@@QEAAAEAU012@$$QEAU012@@Z
	??4InterpolationResult@TerrainModel@BentleyM0200@@QEAAAEAU012@AEBU012@@Z
	??4PointCatchmentResult@TerrainModel@BentleyM0200@@QEAAAEAU012@$$QEAU012@@Z
	??4PointCatchmentResult@TerrainModel@BentleyM0200@@QEAAAEAU012@AEBU012@@Z
	??4PondAnalysisResult@TerrainModel@BentleyM0200@@QEAAAEAU012@$$QEAU012@@Z
	??4PondAnalysisResult@TerrainModel@BentleyM0200@@QEAAAEAU012@AEBU012@@Z
	??4PondCalculation@TerrainModel@BentleyM0200@@QEAAAEAU012@$$QEAU012@@Z
	??4PondCalculation@TerrainModel@BentleyM0200@@QEAAAEAU012@AEBU012@@Z
	??4SlopeAreaResult@TerrainModel@BentleyM0200@@QEAAAEAU012@$$QEAU012@@Z
	??4SlopeAreaResult@TerrainModel@BentleyM0200@@QEAAAEAU012@AEBU012@@Z
	??4TileFilterCriteria@TerrainModel@BentleyM0200@@QEAAAEAV012@$$QEAV012@@Z
	??4TileFilterCriteria@TerrainModel@BentleyM0200@@QEAAAEAV012@AEBV012@@Z
	??4TinFilterCriteria@TerrainModel@BentleyM0200@@QEAAAEAV012@$$QEAV012@@Z
	??4TinFilterCriteria@TerrainModel@BentleyM0200@@QEAAAEAV012@AEBV012@@Z
	??4VisibilityResult@TerrainModel@BentleyM0200@@QEAAAEAU012@$$QEAU012@@Z
	??4VisibilityResult@TerrainModel@BentleyM0200@@QEAAAEAU012@AEBU012@@Z
	??4VolumePolygon@TerrainModel@BentleyM0200@@QEAAAEAV012@$$QEAV012@@Z
	??4VolumePolygon@TerrainModel@BentleyM0200@@QEAAAEAV012@AEBV012@@Z
	??4VolumeRange@TerrainModel@BentleyM0200@@QEAAAEAV012@$$QEAV012@@Z
	??4VolumeRange@TerrainModel@BentleyM0200@@QEAAAEAV012@AEBV012@@Z
	??4VolumeResult@TerrainModel@BentleyM0200@@QEAAAEAU012@$$QEAU012@@Z
	??4VolumeResult@TerrainModel@BentleyM0200@@QEAAAEAU012@AEBU012@@Z
	??8Iterator@DTMFeatureEnumerator@TerrainModel@BentleyM0200@@QEBA_NAEBV0123@@Z
	??8Iterator@DTMMeshEnumerator@TerrainModel@BentleyM0200@@QEBA_NAEBV0123@@Z
	??9Iterator@DTMFeatureEnumerator@TerrainModel@BentleyM0200@@QEBA_NAEBV0123@@Z
	??9Iterator@DTMMeshEnumerator@TerrainModel@BentleyM0200@@QEBA_NAEBV0123@@Z
	??A?$map@_JV?$unique_ptr@VDTMFeatureImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeatureImpl@TerrainModel@BentleyM0200@@@std@@@std@@U?$less@_J@2@V?$allocator@U?$pair@$$CB_JV?$unique_ptr@VDTMFeatureImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeatureImpl@TerrainModel@BentleyM0200@@@std@@@std@@@std@@@2@@std@@QEAAAEAV?$unique_ptr@VDTMFeatureImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeatureImpl@TerrainModel@BentleyM0200@@@std@@@1@AEB_J@Z
	??DIterator@DTMFeatureEnumerator@TerrainModel@BentleyM0200@@QEBA?AVDTMFeature@23@XZ
	??DIterator@DTMMeshEnumerator@TerrainModel@BentleyM0200@@QEBA?AVDTMMesh@23@XZ
	??EIterator@DTMFeatureEnumerator@TerrainModel@BentleyM0200@@QEAA?AV0123@H@Z
	??EIterator@DTMFeatureEnumerator@TerrainModel@BentleyM0200@@QEAAAEAV0123@XZ
	??EIterator@DTMMeshEnumerator@TerrainModel@BentleyM0200@@QEAA?AV0123@H@Z
	??EIterator@DTMMeshEnumerator@TerrainModel@BentleyM0200@@QEAAAEAV0123@XZ
	??H?$_Vb_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@std@@QEBA?AV01@_J@Z
	??R?$default_delete@VDTMImpl@TerrainModel@BentleyM0200@@@std@@QEBAXPEAVDTMImpl@TerrainModel@BentleyM0200@@@Z
	??_7?$_Ref_count_obj2@VDTMPoint@TerrainModel@BentleyM0200@@@std@@6B@
	??_7?$basic_filebuf@DU?$char_traits@D@std@@@std@@6B@
	??_7?$basic_ifstream@DU?$char_traits@D@std@@@std@@6B@
	??_7?$basic_istringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@6B@
	??_7?$basic_ofstream@DU?$char_traits@D@std@@@std@@6B@
	??_7?$basic_stringbuf@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@6B@
	??_7DTMArgumentException@TerrainModel@BentleyM0200@@6B@
	??_7DTMException@TerrainModel@BentleyM0200@@6B@
	??_7DTMFeature@TerrainModel@BentleyM0200@@6B@
	??_7DTMFeatureInfo@TerrainModel@BentleyM0200@@6B@
	??_7DTMFileException@TerrainModel@BentleyM0200@@6B@
	??_7DTMInvalidOperationException@TerrainModel@BentleyM0200@@6B@
	??_7DTMLinearFeature@TerrainModel@BentleyM0200@@6B@
	??_7DTMPointFeature@TerrainModel@BentleyM0200@@6B@
	??_7DTMStandAloneFeatureInfo@TerrainModel@BentleyM0200@@6B@
	??_7bad_alloc@std@@6B@
	??_7bad_array_new_length@std@@6B@
	??_7bad_cast@std@@6B@
	??_7exception@std@@6B@
	??_7runtime_error@std@@6B@
	??_D?$basic_ifstream@DU?$char_traits@D@std@@@std@@QEAAXXZ
	??_D?$basic_istringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAXXZ
	??_D?$basic_ofstream@DU?$char_traits@D@std@@@std@@QEAAXXZ
	?AddFeature@DTMFeatureCache@TerrainModel@BentleyM0200@@QEAA_NW4DTMFeatureType@23@_J1AEBV?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@@Z
	?AddFeature@DTMFeatureCacheImpl@TerrainModel@BentleyM0200@@QEAA_NW4DTMFeatureType@23@_J1AEBV?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@@Z
	?AddLinearFeature@DTM@TerrainModel@BentleyM0200@@QEAA_JAEBV?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@W4DTMFeatureType@23@_J@Z
	?AddLinearFeature@DTMImpl@TerrainModel@BentleyM0200@@QEAA_JAEBV?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@W4DTMFeatureType@23@_J@Z
	?AddPoint@DTMFeature@TerrainModel@BentleyM0200@@QEAA_NNNN@Z
	?AddPoint@DTMSideSlopeInput@TerrainModel@BentleyM0200@@QEAAHAEBVDTMSideSlopeInputPoint@23@@Z
	?AddPoint@DTMSideSlopeInput@TerrainModel@BentleyM0200@@QEAAHNNNN@Z
	?AddPointFeature@DTM@TerrainModel@BentleyM0200@@QEAA_JNNN_J@Z
	?AddPointFeature@DTMImpl@TerrainModel@BentleyM0200@@QEAA_JNNN_J@Z
	?AddPointFeatures@DTM@TerrainModel@BentleyM0200@@QEAA_JAEBV?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@_J@Z
	?AddPointFeatures@DTMImpl@TerrainModel@BentleyM0200@@QEAA_JAEBV?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@_J@Z
	?AnalyzeAspect@DTMFeatureCache@TerrainModel@BentleyM0200@@QEAA_NAEBV?$vector@V?$tuple@NN@std@@V?$allocator@V?$tuple@NN@std@@@2@@std@@_N0@Z
	?AnalyzeElevation@DTMFeatureCache@TerrainModel@BentleyM0200@@QEAA_NAEBV?$vector@V?$tuple@NN@std@@V?$allocator@V?$tuple@NN@std@@@2@@std@@_N0@Z
	?AnalyzePond@DTMPond@TerrainModel@BentleyM0200@@QEAA_NAEBVDTM@23@NAEAUPondAnalysisResult@23@@Z
	?AnalyzePond@DTMPondImpl@TerrainModel@BentleyM0200@@QEAA_NAEBVDTM@23@NAEAUPondAnalysisResult@23@@Z
	?AnalyzePondFromPoint@DTMPond@TerrainModel@BentleyM0200@@QEAA_NAEBVDTM@23@NNAEAUPondAnalysisResult@23@@Z
	?AnalyzeSlope@DTMFeatureCache@TerrainModel@BentleyM0200@@QEAA_NAEBV?$vector@V?$tuple@NN@std@@V?$allocator@V?$tuple@NN@std@@@2@@std@@_N0@Z
	?AzimuthTo@DTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@QEBANAEBV123@@Z
	?BrowseContours@DTMFeatureCache@TerrainModel@BentleyM0200@@QEAA_NNNNN_NHNHAEBV?$vector@V?$tuple@NN@std@@V?$allocator@V?$tuple@NN@std@@@2@@std@@AEBV?$vector@NV?$allocator@N@std@@@5@HN0@Z
	?BrowseDrainageFeatures@DTMFeatureCache@TerrainModel@BentleyM0200@@QEAA_NW4DTMFeatureType@23@AEANAEBV?$vector@V?$tuple@NN@std@@V?$allocator@V?$tuple@NN@std@@@2@@std@@@Z
	?BrowseFeatures@DTMFeatureCache@TerrainModel@BentleyM0200@@QEAA_NW4DTMFeatureType@23@AEBV?$vector@V?$tuple@NN@std@@V?$allocator@V?$tuple@NN@std@@@2@@std@@H@Z
	?BrowseFeatures@DTMFeatureCache@TerrainModel@BentleyM0200@@QEAA_NW4DTMFeatureType@23@H@Z
	?BrowseRadialViewSheds@DTMFeatureCache@TerrainModel@BentleyM0200@@QEAA_NNNNHHN@Z
	?BrowseRegionViewSheds@DTMFeatureCache@TerrainModel@BentleyM0200@@QEAA_NNNN@Z
	?BrowseTinLinesVisibility@DTMFeatureCache@TerrainModel@BentleyM0200@@QEAA_NNNN@Z
	?BrowseTinPointsVisibility@DTMFeatureCache@TerrainModel@BentleyM0200@@QEAA_NNNN@Z
	?CalculateSideSlope@DTMSideSlopeInput@TerrainModel@BentleyM0200@@QEAA_NAEBVDTM@23@AEAN1@Z
	?CalculateSteepestPath@DTMHelpers@TerrainModel@BentleyM0200@@SA_NAEBVDTM@23@NNAEAV?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@HN@Z
	?CalculateVolume@DTM@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@NAEAUDTMVolumeResult@23@@Z
	?CalculateVolume@DTMImpl@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@NAEAUDTMVolumeResult@23@@Z
	?CheckTriangulation@DTM@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@XZ
	?CheckTriangulation@DTMImpl@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@XZ
	?ClearPoints@DTMSideSlopeInput@TerrainModel@BentleyM0200@@QEAAXXZ
	?ClearTriangulation@DTMImpl@TerrainModel@BentleyM0200@@AEAAXXZ
	?Clip@DTM@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@AEBV?$vector@V?$tuple@NN@std@@V?$allocator@V?$tuple@NN@std@@@2@@std@@W4DTMClippingMethod@23@@Z
	?Clip@DTMImpl@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@AEBV?$vector@V?$tuple@NN@std@@V?$allocator@V?$tuple@NN@std@@@2@@std@@W4DTMClippingMethod@23@@Z
	?ComputeExtents@DTMImpl@TerrainModel@BentleyM0200@@AEAA?AW4BentleyStatus@3@XZ
	?ComputeVolumeAndArea@DTMPondImpl@TerrainModel@BentleyM0200@@AEAA_NAEBVDTM@23@@Z
	?ContainsPoint@DTMTriangle@TerrainModel@BentleyM0200@@QEBA_NNN@Z
	?ConvertUnits@DTM@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@NN@Z
	?ConvertUnits@DTMImpl@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@NN@Z
	?Create@DTM@TerrainModel@BentleyM0200@@SA?AV?$unique_ptr@VDTM@TerrainModel@BentleyM0200@@U?$default_delete@VDTM@TerrainModel@BentleyM0200@@@std@@@std@@XZ
	?Create@DTMDrapedLinearElement@TerrainModel@BentleyM0200@@SA?AV?$unique_ptr@VDTMDrapedLinearElement@TerrainModel@BentleyM0200@@U?$default_delete@VDTMDrapedLinearElement@TerrainModel@BentleyM0200@@@std@@@std@@AEBVDTM@23@AEBV?$vector@V?$tuple@NN@std@@V?$allocator@V?$tuple@NN@std@@@2@@5@@Z
	?Create@DTMFeatureEnumerator@TerrainModel@BentleyM0200@@SA?AV?$unique_ptr@VDTMFeatureEnumerator@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeatureEnumerator@TerrainModel@BentleyM0200@@@std@@@std@@AEBVDTM@23@@Z
	?Create@DTMMeshEnumerator@TerrainModel@BentleyM0200@@SA?AV?$unique_ptr@VDTMMeshEnumerator@TerrainModel@BentleyM0200@@U?$default_delete@VDTMMeshEnumerator@TerrainModel@BentleyM0200@@@std@@@std@@AEBVDTM@23@@Z
	?CreateFromDTM@DTMMesh@TerrainModel@BentleyM0200@@QEAA_NAEBVDTM@23@N@Z
	?CreateFromDTM@DTMMeshImpl@TerrainModel@BentleyM0200@@QEAA_NAEBVDTM@23@N@Z
	?CreateFromFile@DTM@TerrainModel@BentleyM0200@@SA?AV?$unique_ptr@VDTM@TerrainModel@BentleyM0200@@U?$default_delete@VDTM@TerrainModel@BentleyM0200@@@std@@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@5@@Z
	?CreateFromGeopakTinFile@DTM@TerrainModel@BentleyM0200@@SA?AV?$unique_ptr@VDTM@TerrainModel@BentleyM0200@@U?$default_delete@VDTM@TerrainModel@BentleyM0200@@@std@@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@5@@Z
	?CreateFromXyzFile@DTM@TerrainModel@BentleyM0200@@SA?AV?$unique_ptr@VDTM@TerrainModel@BentleyM0200@@U?$default_delete@VDTM@TerrainModel@BentleyM0200@@@std@@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@5@@Z
	?CreateIterator@DTMDrapedLinearElement@TerrainModel@BentleyM0200@@QEBA?AV?$unique_ptr@VDTMDrapedLinearElementPointIterator@TerrainModel@BentleyM0200@@U?$default_delete@VDTMDrapedLinearElementPointIterator@TerrainModel@BentleyM0200@@@std@@@std@@XZ
	?CreateLinearFeature@DTMFeature@TerrainModel@BentleyM0200@@SA?AV?$unique_ptr@VDTMFeature@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeature@TerrainModel@BentleyM0200@@@std@@@std@@AEBV?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@5@W4DTMFeatureType@23@_J@Z
	?CreatePointFeature@DTMFeature@TerrainModel@BentleyM0200@@SA?AV?$unique_ptr@VDTMFeature@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeature@TerrainModel@BentleyM0200@@@std@@@std@@NNN_J@Z
	?CreatePond@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEAA?AW4DTMPondResult@23@AEAV?$unique_ptr@VDTM@TerrainModel@BentleyM0200@@U?$default_delete@VDTM@TerrainModel@BentleyM0200@@@std@@@std@@@Z
	?CreatePond@DTMPondDesignCriteriaImpl@TerrainModel@BentleyM0200@@QEAA?AW4DTMPondResult@23@AEAV?$unique_ptr@VDTM@TerrainModel@BentleyM0200@@U?$default_delete@VDTM@TerrainModel@BentleyM0200@@@std@@@std@@AEAN1@Z
	?CreateRegularGrid@DTMMesh@TerrainModel@BentleyM0200@@QEAA_NNNNNNN@Z
	?CreateRegularGrid@DTMMeshImpl@TerrainModel@BentleyM0200@@QEAA_NNNNNNN@Z
	?Delete@DTMTinEditor@TerrainModel@BentleyM0200@@QEAA_NXZ
	?DeleteFeature@DTM@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@_J@Z
	?DeleteFeature@DTMImpl@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@_J@Z
	?DeleteFeatures@DTM@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@AEBV?$vector@_JV?$allocator@_J@std@@@std@@@Z
	?DeleteFeatures@DTMImpl@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@AEBV?$vector@_JV?$allocator@_J@std@@@std@@@Z
	?DeleteFeaturesByType@DTM@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@W4DTMFeatureType@23@@Z
	?DeleteFeaturesByType@DTMImpl@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@W4DTMFeatureType@23@@Z
	?DeleteFeaturesByUserTag@DTM@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@_J@Z
	?DeleteFeaturesByUserTag@DTMImpl@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@_J@Z
	?DeletePoint@DTMFeature@TerrainModel@BentleyM0200@@QEAA_NH@Z
	?DetectFileFormat@DTMHelpers@TerrainModel@BentleyM0200@@SA?AW4DTMFileFormat@23@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	?DistanceTo@DTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@QEBANAEBV123@@Z
	?DownsamplePointCloud@DTMHelpers@TerrainModel@BentleyM0200@@SA_NAEBV?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@AEAV45@N@Z
	?EmptyCache@DTMFeatureCache@TerrainModel@BentleyM0200@@QEAAXXZ
	?EmptyCache@DTMFeatureCacheImpl@TerrainModel@BentleyM0200@@QEAAXXZ
	?ExcludeAllFeatures@DTMFeatureEnumerator@TerrainModel@BentleyM0200@@QEAAXXZ
	?ExcludeFeature@DTMFeatureEnumerator@TerrainModel@BentleyM0200@@QEAAXW4DTMFeatureType@23@@Z
	?ExportSlopeLines@DTMSideSlopeInput@TerrainModel@BentleyM0200@@QEAA_NAEAV?$vector@V?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@V?$allocator@V?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@@2@@std@@0@Z
	?ExportSlopeLines@DTMSideSlopeInputImpl@TerrainModel@BentleyM0200@@QEAA_NAEAV?$vector@V?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@V?$allocator@V?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@@2@@std@@0@Z
	?ExportToFile@DTMPond@TerrainModel@BentleyM0200@@QEBA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	?ExportToFile@DTMPondImpl@TerrainModel@BentleyM0200@@QEBA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	?FilterPointCloud@DTMHelpers@TerrainModel@BentleyM0200@@SA_NAEBV?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@AEAV45@NH@Z
	?FindPondBoundary@DTMPondImpl@TerrainModel@BentleyM0200@@AEAA_NAEBVDTM@23@@Z
	?FireCallback@DTMFeatureCache@TerrainModel@BentleyM0200@@QEAA_NXZ
	?GetArea@CutFillResult@TerrainModel@BentleyM0200@@QEBANXZ
	?GetArea@PondCalculation@TerrainModel@BentleyM0200@@QEBANXZ
	?GetArea@SlopeAreaResult@TerrainModel@BentleyM0200@@QEBANXZ
	?GetAspectAtPoint@DTM@TerrainModel@BentleyM0200@@QEBA?AW4BentleyStatus@3@NNAEAN@Z
	?GetAspectAtPoint@DTMImpl@TerrainModel@BentleyM0200@@QEBA?AW4BentleyStatus@3@NNAEAN@Z
	?GetBalanceVolume@CutFillResult@TerrainModel@BentleyM0200@@QEBANXZ
	?GetBermSlope@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEBANXZ
	?GetBermToTargetDtmSlope@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEBANXZ
	?GetBermWidth@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEBANXZ
	?GetCacheSize@BrowsingCriteria@TerrainModel@BentleyM0200@@QEBAJXZ
	?GetCalculated@PondCalculation@TerrainModel@BentleyM0200@@QEBA_NXZ
	?GetCatchment@PointCatchmentResult@TerrainModel@BentleyM0200@@QEBAAEBV?$vector@VDTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@V?$allocator@VDTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@@std@@@std@@XZ
	?GetCellArea@VolumeResult@TerrainModel@BentleyM0200@@QEBANXZ
	?GetCode@DTMDrapedLinearElementPoint@TerrainModel@BentleyM0200@@QEBA?AW4DTMDrapedLinearElementPointCode@23@XZ
	?GetCoordinates@DTMDrapedLinearElementPoint@TerrainModel@BentleyM0200@@QEBAAEBV?$tuple@NNN@std@@XZ
	?GetCoordinates@DTMPointFeature@TerrainModel@BentleyM0200@@QEBA_NAEAN00@Z
	?GetCornerStrokeTolerance@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEBANXZ
	?GetCount@DTMFeatureEnumerator@TerrainModel@BentleyM0200@@QEBAHXZ
	?GetCount@DTMMeshEnumerator@TerrainModel@BentleyM0200@@QEBAHXZ
	?GetCrownWidth@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEBANXZ
	?GetCurrent@DTMDrapedLinearElementPointIterator@TerrainModel@BentleyM0200@@QEBA?AVDTMDrapedLinearElementPoint@23@XZ
	?GetCurrent@DTMDrapedLinearElementPointIteratorImpl@TerrainModel@BentleyM0200@@QEBA?AVDTMDrapedLinearElementPoint@23@XZ
	?GetCut@VolumeRange@TerrainModel@BentleyM0200@@QEBANXZ
	?GetCutArea@CutFillResult@TerrainModel@BentleyM0200@@QEBANXZ
	?GetCutVolume@CutFillResult@TerrainModel@BentleyM0200@@QEBANXZ
	?GetDepth@PondCalculation@TerrainModel@BentleyM0200@@QEBANXZ
	?GetDepthGrid@DTMPond@TerrainModel@BentleyM0200@@QEBA_NAEAV?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@N@Z
	?GetDepthGrid@DTMPondImpl@TerrainModel@BentleyM0200@@QEBA_NAEAV?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@N@Z
	?GetDesignMethod@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEBA?AW4DTMPondDesignMethod@23@XZ
	?GetDistanceAlong@DTMDrapedLinearElementPoint@TerrainModel@BentleyM0200@@QEBANXZ
	?GetDtmFeatureId@DTMDrapePointFeature@TerrainModel@BentleyM0200@@QEBA_JXZ
	?GetDtmFeatureIndex@DTMDrapePointFeature@TerrainModel@BentleyM0200@@QEBAHXZ
	?GetDtmFeatureNextPoint@DTMDrapePointFeature@TerrainModel@BentleyM0200@@QEBAHXZ
	?GetDtmFeaturePriorPoint@DTMDrapePointFeature@TerrainModel@BentleyM0200@@QEBAHXZ
	?GetDtmFeatureType@DTMDrapePointFeature@TerrainModel@BentleyM0200@@QEBA?AW4DTMFeatureType@23@XZ
	?GetDtmUserTag@DTMDrapePointFeature@TerrainModel@BentleyM0200@@QEBA_JXZ
	?GetElevation@PondCalculation@TerrainModel@BentleyM0200@@QEBANXZ
	?GetElevationAtPoint@DTM@TerrainModel@BentleyM0200@@QEBA?AW4BentleyStatus@3@NNAEAN@Z
	?GetElevationAtPoint@DTMImpl@TerrainModel@BentleyM0200@@QEBA?AW4BentleyStatus@3@NNAEAN@Z
	?GetElevationRange@DTM@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@AEAN0@Z
	?GetElevationRange@DTMImpl@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@AEAN0@Z
	?GetExtents@DTM@TerrainModel@BentleyM0200@@QEBA?AW4BentleyStatus@3@AEAN00000@Z
	?GetExtents@DTMFeature@TerrainModel@BentleyM0200@@QEBA_NAEAN00000@Z
	?GetExtents@DTMImpl@TerrainModel@BentleyM0200@@QEBA?AW4BentleyStatus@3@AEAN00000@Z
	?GetExtents@DTMMesh@TerrainModel@BentleyM0200@@QEBA_NAEAN00000@Z
	?GetExtents@DTMMeshImpl@TerrainModel@BentleyM0200@@QEBA_NAEAN00000@Z
	?GetFace@DTMMesh@TerrainModel@BentleyM0200@@QEBA_NHAEAH00@Z
	?GetFace@DTMMeshImpl@TerrainModel@BentleyM0200@@QEBA_NHAEAH00@Z
	?GetFaceCount@DTMMesh@TerrainModel@BentleyM0200@@QEBAHXZ
	?GetFeatureAt@DTMFeatureEnumerator@TerrainModel@BentleyM0200@@QEBA?AVDTMFeature@23@H@Z
	?GetFeatureCount@DTM@TerrainModel@BentleyM0200@@QEBAJXZ
	?GetFeatureId@DTMStandAloneFeatureInfo@TerrainModel@BentleyM0200@@UEBA_JXZ
	?GetFeatureIds@DTMDrapedLinearElementPoint@TerrainModel@BentleyM0200@@QEBAAEBV?$vector@_JV?$allocator@_J@std@@@std@@XZ
	?GetFeaturePoints@DTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@QEBAAEBV?$vector@UDPoint3d@BentleyM0200@@V?$allocator@UDPoint3d@BentleyM0200@@@std@@@std@@XZ
	?GetFeatureType@DTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@QEBA?AW4DTMDynamicFeatureType@23@XZ
	?GetFeatureType@DTMFeature@TerrainModel@BentleyM0200@@QEBA?AW4DTMFeatureType@23@XZ
	?GetFeatureType@DTMStandAloneFeatureInfo@TerrainModel@BentleyM0200@@UEBA?AW4DTMFeatureType@23@XZ
	?GetFeatures@DTMDrapedLinearElementPoint@TerrainModel@BentleyM0200@@QEBAAEBV?$vector@UDTMDrapePointFeature@TerrainModel@BentleyM0200@@V?$allocator@UDTMDrapePointFeature@TerrainModel@BentleyM0200@@@std@@@std@@XZ
	?GetFeatures@VisibilityResult@TerrainModel@BentleyM0200@@QEBAAEBV?$vector@VDTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@V?$allocator@VDTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@@std@@@std@@XZ
	?GetFenceOption@BrowsingCriteria@TerrainModel@BentleyM0200@@QEBA?AW4DTMFenceOption@23@XZ
	?GetFencePoints@BrowsingCriteria@TerrainModel@BentleyM0200@@QEBAAEBV?$vector@UDPoint3d@BentleyM0200@@V?$allocator@UDPoint3d@BentleyM0200@@@std@@@std@@XZ
	?GetFenceType@BrowsingCriteria@TerrainModel@BentleyM0200@@QEBA?AW4DTMFenceType@23@XZ
	?GetFill@VolumeRange@TerrainModel@BentleyM0200@@QEBANXZ
	?GetFillArea@CutFillResult@TerrainModel@BentleyM0200@@QEBANXZ
	?GetFillVolume@CutFillResult@TerrainModel@BentleyM0200@@QEBANXZ
	?GetFilterOption@TinFilterCriteria@TerrainModel@BentleyM0200@@QEBA?AW4FilterOption@23@XZ
	?GetFilterReduction@FilterResult@TerrainModel@BentleyM0200@@QEBANXZ
	?GetFreeBoard@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEBANXZ
	?GetFromArea@VolumeResult@TerrainModel@BentleyM0200@@QEBANXZ
	?GetHigh@VolumeRange@TerrainModel@BentleyM0200@@QEBANXZ
	?GetId@DTMFeature@TerrainModel@BentleyM0200@@QEBA_JXZ
	?GetInsideSlope@DTMSideSlopeInput@TerrainModel@BentleyM0200@@QEBANXZ
	?GetLastResult@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEBA?AW4DTMPondResult@23@XZ
	?GetLength@DTMFeature@TerrainModel@BentleyM0200@@QEBANXZ
	?GetLow@VolumeRange@TerrainModel@BentleyM0200@@QEBANXZ
	?GetMaxTileDivide@TileFilterCriteria@TerrainModel@BentleyM0200@@QEBAJXZ
	?GetMaxTilePoints@TileFilterCriteria@TerrainModel@BentleyM0200@@QEBAJXZ
	?GetMeshAt@DTMMeshEnumerator@TerrainModel@BentleyM0200@@QEBA?AVDTMMesh@23@H@Z
	?GetNumCellsUsed@VolumeResult@TerrainModel@BentleyM0200@@QEBAHXZ
	?GetNumDtmFeatures@InterpolationResult@TerrainModel@BentleyM0200@@QEBAHXZ
	?GetNumDtmFeaturesInterpolated@InterpolationResult@TerrainModel@BentleyM0200@@QEBAHXZ
	?GetNumPoints@VolumePolygon@TerrainModel@BentleyM0200@@QEBAHXZ
	?GetNumVolumePolygons@VolumeResult@TerrainModel@BentleyM0200@@QEBA_KXZ
	?GetNumberOfPointsAfterFiltering@FilterResult@TerrainModel@BentleyM0200@@QEBAHXZ
	?GetNumberOfPointsBeforeFiltering@FilterResult@TerrainModel@BentleyM0200@@QEBAHXZ
	?GetOutsideSlope@DTMSideSlopeInput@TerrainModel@BentleyM0200@@QEBANXZ
	?GetPoint@DTMFeature@TerrainModel@BentleyM0200@@QEBA_NHAEAN00@Z
	?GetPoint@DTMSideSlopeInput@TerrainModel@BentleyM0200@@QEBAAEBVDTMSideSlopeInputPoint@23@H@Z
	?GetPointByIndex@DTMDrapedLinearElement@TerrainModel@BentleyM0200@@QEBA?AVDTMDrapedLinearElementPoint@23@H@Z
	?GetPointByIndex@DTMDrapedLinearElementImpl@TerrainModel@BentleyM0200@@QEBA?AVDTMDrapedLinearElementPoint@23@H@Z
	?GetPointCount@DTM@TerrainModel@BentleyM0200@@QEBAJXZ
	?GetPointCount@DTMDrapedLinearElement@TerrainModel@BentleyM0200@@QEBAHXZ
	?GetPointCount@DTMFeature@TerrainModel@BentleyM0200@@QEBAHXZ
	?GetPointCount@DTMSideSlopeInput@TerrainModel@BentleyM0200@@QEBAHXZ
	?GetPoints@DTMFeature@TerrainModel@BentleyM0200@@QEBA_NAEAV?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@@Z
	?GetPoints@DTMStandAloneFeatureInfo@TerrainModel@BentleyM0200@@UEBA?AV?$vector@UDPoint3d@BentleyM0200@@V?$allocator@UDPoint3d@BentleyM0200@@@std@@@std@@XZ
	?GetPoints@VolumePolygon@TerrainModel@BentleyM0200@@QEBAAEBV?$vector@UDPoint3d@BentleyM0200@@V?$allocator@UDPoint3d@BentleyM0200@@@std@@@std@@XZ
	?GetPondBoundary@DTMPond@TerrainModel@BentleyM0200@@QEBA_NAEAV?$vector@V?$tuple@NN@std@@V?$allocator@V?$tuple@NN@std@@@2@@std@@@Z
	?GetPondElevation@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEBANXZ
	?GetPondFeatures@PondCalculation@TerrainModel@BentleyM0200@@QEBAAEBV?$vector@VDTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@V?$allocator@VDTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@@std@@@std@@XZ
	?GetPondTarget@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEBA?AW4DTMPondTarget@23@XZ
	?GetPondVolume@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEBANXZ
	?GetReadSourceFeatures@DTMFeatureEnumerator@TerrainModel@BentleyM0200@@QEBA_NXZ
	?GetReferencePoints@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEBAAEBV?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@XZ
	?GetReinsertOption@TinFilterCriteria@TerrainModel@BentleyM0200@@QEBA_NXZ
	?GetSelectedFeaturePoints@DTMTinEditor@TerrainModel@BentleyM0200@@QEBA_NAEAV?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@@Z
	?GetSideSlope@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEBANXZ
	?GetSlopeAtPoint@DTM@TerrainModel@BentleyM0200@@QEBA?AW4BentleyStatus@3@NNAEAN@Z
	?GetSlopeAtPoint@DTMImpl@TerrainModel@BentleyM0200@@QEBA?AW4BentleyStatus@3@NNAEAN@Z
	?GetSlopeType@DTMSideSlopeInput@TerrainModel@BentleyM0200@@QEBA?AW4SlopeType@23@XZ
	?GetSort@DTMFeatureEnumerator@TerrainModel@BentleyM0200@@QEBA_NXZ
	?GetState@DTM@TerrainModel@BentleyM0200@@QEBA?AW4DTMState@23@XZ
	?GetStatistics@DTM@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@AEAUDTMStatistics@23@@Z
	?GetStatistics@DTMImpl@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@AEAUDTMStatistics@23@@Z
	?GetSumpPoint@PointCatchmentResult@TerrainModel@BentleyM0200@@QEBA?AUDPoint3d@3@XZ
	?GetSurfaceDistance@DTM@TerrainModel@BentleyM0200@@QEBA?AW4BentleyStatus@3@NNNNAEAN@Z
	?GetSurfaceDistance@DTMImpl@TerrainModel@BentleyM0200@@QEBA?AW4BentleyStatus@3@NNNNAEAN@Z
	?GetTargetDTM@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEBAPEBVDTM@23@XZ
	?GetTargetElevation@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEBANXZ
	?GetTargetVolume@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEBANXZ
	?GetTileLength@TileFilterCriteria@TerrainModel@BentleyM0200@@QEBANXZ
	?GetToArea@VolumeResult@TerrainModel@BentleyM0200@@QEBANXZ
	?GetTriangleCount@DTM@TerrainModel@BentleyM0200@@QEBAJXZ
	?GetUserTag@DTMFeature@TerrainModel@BentleyM0200@@QEBA_JXZ
	?GetUserTag@DTMStandAloneFeatureInfo@TerrainModel@BentleyM0200@@UEBA_JXZ
	?GetUserTagFilterRange@DTMFeatureEnumerator@TerrainModel@BentleyM0200@@QEBAXAEA_J0@Z
	?GetVertex@DTMMesh@TerrainModel@BentleyM0200@@QEBA_NHAEAN00@Z
	?GetVertex@DTMMeshImpl@TerrainModel@BentleyM0200@@QEBA_NHAEAN00@Z
	?GetVertexCount@DTMMesh@TerrainModel@BentleyM0200@@QEBAHXZ
	?GetVisibility@VisibilityResult@TerrainModel@BentleyM0200@@QEBA?AW4VisibilityType@23@XZ
	?GetVolume@PondCalculation@TerrainModel@BentleyM0200@@QEBANXZ
	?GetVolumePolygons@VolumeResult@TerrainModel@BentleyM0200@@QEBAAEBV?$vector@VVolumePolygon@TerrainModel@BentleyM0200@@V?$allocator@VVolumePolygon@TerrainModel@BentleyM0200@@@std@@@std@@XZ
	?GetWidth@DTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@QEBANXZ
	?GetX@DTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@QEBANXZ
	?GetY@DTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@QEBANXZ
	?GetZ@DTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@QEBANXZ
	?GetZTolerance@TileFilterCriteria@TerrainModel@BentleyM0200@@QEBANXZ
	?GetZTolerance@TinFilterCriteria@TerrainModel@BentleyM0200@@QEBANXZ
	?HorizontalDistanceTo@DTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@QEBANAEBV123@@Z
	?IncludeAllFeatures@DTMFeatureEnumerator@TerrainModel@BentleyM0200@@QEAAXXZ
	?IncludeAllFeatures@DTMFeatureEnumeratorImpl@TerrainModel@BentleyM0200@@QEAAXXZ
	?IncludeFeature@DTMFeatureEnumerator@TerrainModel@BentleyM0200@@QEAAXW4DTMFeatureType@23@@Z
	?IsBerm@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEBA_NXZ
	?IsBermFillOnly@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEBA_NXZ
	?IsClosed@DTMLinearFeature@TerrainModel@BentleyM0200@@QEBA_NXZ
	?IsCrown@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEBA_NXZ
	?IsValid@DTMDrapedLinearElementPointIterator@TerrainModel@BentleyM0200@@QEBA_NXZ
	?IsValid@DTMFeature@TerrainModel@BentleyM0200@@QEBA_NXZ
	?JoinFeatures@DTM@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@W4DTMFeatureType@23@N@Z
	?JoinFeatures@DTMImpl@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@W4DTMFeatureType@23@N@Z
	?LoadFeatures@DTMFeatureEnumeratorImpl@TerrainModel@BentleyM0200@@QEAAXXZ
	?LoadFromFile@DTM@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	?LoadFromFile@DTMImpl@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	?LoadFromFile@DTMMesh@TerrainModel@BentleyM0200@@QEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	?LoadFromFile@DTMMeshImpl@TerrainModel@BentleyM0200@@QEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	?LoadFromGeopakTinFile@DTM@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	?LoadFromGeopakTinFile@DTMImpl@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	?LoadFromXyzFile@DTM@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	?LoadFromXyzFile@DTMImpl@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	?LoadMeshes@DTMMeshEnumeratorImpl@TerrainModel@BentleyM0200@@QEAAXXZ
	?LonLatToXY@DTMHelpers@TerrainModel@BentleyM0200@@SA_NNNAEAN0H@Z
	?Merge@DTM@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@AEBV123@@Z
	?Merge@DTMImpl@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@AEBV123@@Z
	?MoveNext@DTMDrapedLinearElementPointIterator@TerrainModel@BentleyM0200@@QEAA_NXZ
	?OffsetElevation@DTM@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@N@Z
	?OffsetElevation@DTMImpl@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@N@Z
	?PerformLineOfSightAnalysis@DTMHelpers@TerrainModel@BentleyM0200@@SA_NAEBVDTM@23@NNNNNNAEA_NAEAV?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@@Z
	?PerformWatershedAnalysis@DTMHelpers@TerrainModel@BentleyM0200@@SA_NAEBVDTM@23@NNAEAV?$vector@V?$tuple@NN@std@@V?$allocator@V?$tuple@NN@std@@@2@@std@@AEAV?$vector@V?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@V?$allocator@V?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@@2@@6@@Z
	?ReadXYZFile@DTMHelpers@TerrainModel@BentleyM0200@@SA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAV?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@5@@Z
	?RemoveHull@DTM@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@XZ
	?RemoveHull@DTMImpl@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@XZ
	?Reset@DTMDrapedLinearElementPointIterator@TerrainModel@BentleyM0200@@QEAAXXZ
	?Reverse@DTMLinearFeature@TerrainModel@BentleyM0200@@QEAA_NXZ
	?SaveToFile@DTM@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	?SaveToFile@DTMImpl@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	?SaveToFile@DTMMesh@TerrainModel@BentleyM0200@@QEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	?SaveToFile@DTMMeshImpl@TerrainModel@BentleyM0200@@QEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	?SaveToGeopakTinFile@DTM@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	?SaveToGeopakTinFile@DTMImpl@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	?Select@DTMTinEditor@TerrainModel@BentleyM0200@@QEAA_NW4DTMFeatureType@23@NNN@Z
	?SetBerm@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEAAX_N@Z
	?SetBermFillOnly@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEAAX_N@Z
	?SetBermSlope@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEAAXN@Z
	?SetBermToTargetDtmSlope@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEAAXN@Z
	?SetBermWidth@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEAAXN@Z
	?SetCacheSize@BrowsingCriteria@TerrainModel@BentleyM0200@@QEAAXJ@Z
	?SetClosed@DTMLinearFeature@TerrainModel@BentleyM0200@@QEAA_N_N@Z
	?SetCoordinates@DTMPointFeature@TerrainModel@BentleyM0200@@QEAA_NNNN@Z
	?SetCornerStrokeTolerance@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEAAXN@Z
	?SetCrown@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEAAX_N@Z
	?SetCrownWidth@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEAAXN@Z
	?SetCutFillValues@VolumeRange@TerrainModel@BentleyM0200@@QEAAXNN@Z
	?SetDesignMethod@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEAAXW4DTMPondDesignMethod@23@@Z
	?SetFenceOption@BrowsingCriteria@TerrainModel@BentleyM0200@@QEAAXW4DTMFenceOption@23@@Z
	?SetFencePoints@BrowsingCriteria@TerrainModel@BentleyM0200@@QEAAXAEBV?$vector@UDPoint3d@BentleyM0200@@V?$allocator@UDPoint3d@BentleyM0200@@@std@@@std@@@Z
	?SetFenceToBlock@BrowsingCriteria@TerrainModel@BentleyM0200@@QEAAXAEBUDRange3d@3@@Z
	?SetFenceType@BrowsingCriteria@TerrainModel@BentleyM0200@@QEAAXW4DTMFenceType@23@@Z
	?SetFilterOption@TinFilterCriteria@TerrainModel@BentleyM0200@@QEAAXW4FilterOption@23@@Z
	?SetFreeBoard@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEAAXN@Z
	?SetHigh@VolumeRange@TerrainModel@BentleyM0200@@QEAAXN@Z
	?SetInsideSlope@DTMSideSlopeInput@TerrainModel@BentleyM0200@@QEAAXN@Z
	?SetLow@VolumeRange@TerrainModel@BentleyM0200@@QEAAXN@Z
	?SetMaxTileDivide@TileFilterCriteria@TerrainModel@BentleyM0200@@QEAAXJ@Z
	?SetMaxTilePoints@TileFilterCriteria@TerrainModel@BentleyM0200@@QEAAXJ@Z
	?SetMaxTriangles@DTMMeshEnumerator@TerrainModel@BentleyM0200@@QEAAXH@Z
	?SetOutsideSlope@DTMSideSlopeInput@TerrainModel@BentleyM0200@@QEAAXN@Z
	?SetPoint@DTMFeature@TerrainModel@BentleyM0200@@QEAA_NHNNN@Z
	?SetPondTarget@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEAAXW4DTMPondTarget@23@@Z
	?SetReadSourceFeatures@DTMFeatureEnumerator@TerrainModel@BentleyM0200@@QEAAX_N@Z
	?SetReferencePoints@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEAAXAEBV?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@@Z
	?SetReinsertOption@TinFilterCriteria@TerrainModel@BentleyM0200@@QEAAX_N@Z
	?SetSideSlope@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEAAXN@Z
	?SetSlopeType@DTMSideSlopeInput@TerrainModel@BentleyM0200@@QEAAXW4SlopeType@23@@Z
	?SetSort@DTMFeatureEnumerator@TerrainModel@BentleyM0200@@QEAAX_N@Z
	?SetTargetDTM@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEAAXPEBVDTM@23@@Z
	?SetTargetElevation@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEAAXN@Z
	?SetTargetVolume@DTMPondDesignCriteria@TerrainModel@BentleyM0200@@QEAAXN@Z
	?SetTileLength@TileFilterCriteria@TerrainModel@BentleyM0200@@QEAAXN@Z
	?SetTriangulationParameters@DTM@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@NNW4DTMEdgeOption@23@N@Z
	?SetTriangulationParameters@DTMImpl@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@NNW4DTMEdgeOption@23@N@Z
	?SetUsePolyfaceHeader@DTMMeshEnumerator@TerrainModel@BentleyM0200@@QEAAX_N@Z
	?SetUserTag@DTMFeature@TerrainModel@BentleyM0200@@QEAAX_J@Z
	?SetUserTagFilterRange@DTMFeatureEnumerator@TerrainModel@BentleyM0200@@QEAAXAEBUDTMUserTagRange@23@@Z
	?SetUserTagFilterRange@DTMFeatureEnumerator@TerrainModel@BentleyM0200@@QEAAX_J0@Z
	?SetWidth@DTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@QEAAXN@Z
	?SetX@DTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@QEAAXN@Z
	?SetY@DTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@QEAAXN@Z
	?SetZ@DTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@QEAAXN@Z
	?SetZTolerance@TileFilterCriteria@TerrainModel@BentleyM0200@@QEAAXN@Z
	?SetZTolerance@TinFilterCriteria@TerrainModel@BentleyM0200@@QEAAXN@Z
	?Simplify@DTMLinearFeature@TerrainModel@BentleyM0200@@QEAA_NN@Z
	?Simplify@DTMMesh@TerrainModel@BentleyM0200@@QEAA_NN@Z
	?Simplify@DTMMeshImpl@TerrainModel@BentleyM0200@@QEAA_NN@Z
	?Smooth@DTMLinearFeature@TerrainModel@BentleyM0200@@QEAA_NN@Z
	?Smooth@DTMMesh@TerrainModel@BentleyM0200@@QEAA_NHN@Z
	?Smooth@DTMMeshImpl@TerrainModel@BentleyM0200@@QEAA_NHN@Z
	?Transform@DTM@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@AEBV?$vector@NV?$allocator@N@std@@@std@@@Z
	?Transform@DTMImpl@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@AEBV?$vector@NV?$allocator@N@std@@@std@@@Z
	?Triangulate@DTM@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@XZ
	?Triangulate@DTMImpl@TerrainModel@BentleyM0200@@QEAA?AW4BentleyStatus@3@XZ
	?UpdateExtents@DTMMeshImpl@TerrainModel@BentleyM0200@@AEAAXXZ
	?UpdateStatistics@DTMImpl@TerrainModel@BentleyM0200@@AEAAXXZ
	?WriteXYZFile@DTMHelpers@TerrainModel@BentleyM0200@@SA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBV?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@5@@Z
	?XYToLonLat@DTMHelpers@TerrainModel@BentleyM0200@@SA_NNNAEAN0H@Z
	?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBW4DTMFeatureType@TerrainModel@BentleyM0200@@_N@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBW4DTMFeatureType@TerrainModel@BentleyM0200@@_N@std@@@std@@@std@@@2@@Z
	?_Change_array@?$vector@UDPoint3d@BentleyM0200@@V?$allocator@UDPoint3d@BentleyM0200@@@std@@@std@@AEAAXQEAUDPoint3d@BentleyM0200@@_K1@Z
	?_Change_array@?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@AEAAXQEAV?$tuple@NNN@2@_K1@Z
	?_Change_array@?$vector@VDTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@V?$allocator@VDTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@@std@@@std@@AEAAXQEAVDTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@_K1@Z
	?_Delete_this@?$_Ref_count_obj2@VDTMPoint@TerrainModel@BentleyM0200@@@std@@EEAAXXZ
	?_Destroy@?$_Ref_count_obj2@VDTMPoint@TerrainModel@BentleyM0200@@@std@@EEAAXXZ
	?_Endwrite@?$basic_filebuf@DU?$char_traits@D@std@@@std@@IEAA_NXZ
	?_Extract@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CB_JV?$unique_ptr@VDTMFeatureImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeatureImpl@TerrainModel@BentleyM0200@@@std@@@std@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CB_JV?$unique_ptr@VDTMFeatureImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeatureImpl@TerrainModel@BentleyM0200@@@std@@@std@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CB_JV?$unique_ptr@VDTMFeatureImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeatureImpl@TerrainModel@BentleyM0200@@@std@@@std@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z
	?_Forced_rehash@?$_Hash@V?$_Umap_traits@W4DTMFeatureType@TerrainModel@BentleyM0200@@_NV?$_Uhash_compare@W4DTMFeatureType@TerrainModel@BentleyM0200@@U?$hash@W4DTMFeatureType@TerrainModel@BentleyM0200@@@std@@U?$equal_to@W4DTMFeatureType@TerrainModel@BentleyM0200@@@5@@std@@V?$allocator@U?$pair@$$CBW4DTMFeatureType@TerrainModel@BentleyM0200@@_N@std@@@5@$0A@@std@@@std@@IEAAX_K@Z
	?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z
	?_Insert_node@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBU?$pair@HH@std@@V?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@2@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBU?$pair@HH@std@@V?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@2@@std@@PEAX@2@U?$_Tree_id@PEAU?$_Tree_node@U?$pair@$$CBU?$pair@HH@std@@V?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@2@@std@@PEAX@std@@@2@QEAU32@@Z
	?_Insert_node@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CB_JV?$unique_ptr@VDTMFeatureImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeatureImpl@TerrainModel@BentleyM0200@@@std@@@std@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CB_JV?$unique_ptr@VDTMFeatureImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeatureImpl@TerrainModel@BentleyM0200@@@std@@@std@@@std@@PEAX@2@U?$_Tree_id@PEAU?$_Tree_node@U?$pair@$$CB_JV?$unique_ptr@VDTMFeatureImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeatureImpl@TerrainModel@BentleyM0200@@@std@@@std@@@std@@PEAX@std@@@2@QEAU32@@Z
	?_Insert_x@?$vector@_NV?$allocator@_N@std@@@std@@QEAA_KV?$_Vb_const_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@_K@Z
	?_Lock@?$basic_filebuf@DU?$char_traits@D@std@@@std@@UEAAXXZ
	?_Lrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CB_JV?$unique_ptr@VDTMFeatureImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeatureImpl@TerrainModel@BentleyM0200@@@std@@@std@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CB_JV?$unique_ptr@VDTMFeatureImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeatureImpl@TerrainModel@BentleyM0200@@@std@@@std@@@std@@PEAX@2@@Z
	?_Rehash_for_1@?$_Hash@V?$_Umap_traits@W4DTMFeatureType@TerrainModel@BentleyM0200@@_NV?$_Uhash_compare@W4DTMFeatureType@TerrainModel@BentleyM0200@@U?$hash@W4DTMFeatureType@TerrainModel@BentleyM0200@@@std@@U?$equal_to@W4DTMFeatureType@TerrainModel@BentleyM0200@@@5@@std@@V?$allocator@U?$pair@$$CBW4DTMFeatureType@TerrainModel@BentleyM0200@@_N@std@@@5@$0A@@std@@@std@@IEAAXXZ
	?_Reset_back@?$basic_filebuf@DU?$char_traits@D@std@@@std@@AEAAXXZ
	?_Rrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CB_JV?$unique_ptr@VDTMFeatureImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeatureImpl@TerrainModel@BentleyM0200@@@std@@@std@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CB_JV?$unique_ptr@VDTMFeatureImpl@TerrainModel@BentleyM0200@@U?$default_delete@VDTMFeatureImpl@TerrainModel@BentleyM0200@@@std@@@std@@@std@@PEAX@2@@Z
	?_Throw_bad_array_new_length@std@@YAXXZ
	?_Throw_bad_cast@std@@YAXXZ
	?_Throw_tree_length_error@std@@YAXXZ
	?_Tidy@?$vector@HV?$allocator@H@std@@@std@@AEAAXXZ
	?_Tidy@?$vector@UDPoint3d@BentleyM0200@@V?$allocator@UDPoint3d@BentleyM0200@@@std@@@std@@AEAAXXZ
	?_Tidy@?$vector@UDTMDrapePointFeature@TerrainModel@BentleyM0200@@V?$allocator@UDTMDrapePointFeature@TerrainModel@BentleyM0200@@@std@@@std@@AEAAXXZ
	?_Tidy@?$vector@V?$tuple@HHH@std@@V?$allocator@V?$tuple@HHH@std@@@2@@std@@AEAAXXZ
	?_Tidy@?$vector@V?$tuple@NN@std@@V?$allocator@V?$tuple@NN@std@@@2@@std@@AEAAXXZ
	?_Tidy@?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@AEAAXXZ
	?_Tidy@?$vector@VDTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@V?$allocator@VDTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@@std@@@std@@AEAAXXZ
	?_Tidy@?$vector@VDTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@V?$allocator@VDTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@@std@@@std@@AEAAXXZ
	?_Tidy@?$vector@VVolumePolygon@TerrainModel@BentleyM0200@@V?$allocator@VVolumePolygon@TerrainModel@BentleyM0200@@@std@@@std@@AEAAXXZ
	?_Tidy@?$vector@_JV?$allocator@_J@std@@@std@@AEAAXXZ
	?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ
	?_Unchecked_erase@?$_Hash@V?$_Umap_traits@W4DTMFeatureType@TerrainModel@BentleyM0200@@_NV?$_Uhash_compare@W4DTMFeatureType@TerrainModel@BentleyM0200@@U?$hash@W4DTMFeatureType@TerrainModel@BentleyM0200@@@std@@U?$equal_to@W4DTMFeatureType@TerrainModel@BentleyM0200@@@5@@std@@V?$allocator@U?$pair@$$CBW4DTMFeatureType@TerrainModel@BentleyM0200@@_N@std@@@5@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@$$CBW4DTMFeatureType@TerrainModel@BentleyM0200@@_N@std@@PEAX@2@PEAU32@QEAU32@@Z
	?_Unlock@?$basic_filebuf@DU?$char_traits@D@std@@@std@@UEAAXXZ
	?_Xlen@?$vector@_NV?$allocator@_N@std@@@std@@SAXXZ
	?_Xlen_string@std@@YAXXZ
	?_Xlength@?$vector@HV?$allocator@H@std@@@std@@CAXXZ
	?_Xlength@?$vector@IV?$allocator@I@std@@@std@@CAXXZ
	?_Xlength@?$vector@NV?$allocator@N@std@@@std@@CAXXZ
	?_Xlength@?$vector@UDPoint3d@BentleyM0200@@V?$allocator@UDPoint3d@BentleyM0200@@@std@@@std@@CAXXZ
	?_Xlength@?$vector@UDTMCachedFeature@TerrainModel@BentleyM0200@@V?$allocator@UDTMCachedFeature@TerrainModel@BentleyM0200@@@std@@@std@@CAXXZ
	?_Xlength@?$vector@UDTMDrapePointFeature@TerrainModel@BentleyM0200@@V?$allocator@UDTMDrapePointFeature@TerrainModel@BentleyM0200@@@std@@@std@@CAXXZ
	?_Xlength@?$vector@V?$shared_ptr@VDTMPoint@TerrainModel@BentleyM0200@@@std@@V?$allocator@V?$shared_ptr@VDTMPoint@TerrainModel@BentleyM0200@@@std@@@2@@std@@CAXXZ
	?_Xlength@?$vector@V?$tuple@HHH@std@@V?$allocator@V?$tuple@HHH@std@@@2@@std@@CAXXZ
	?_Xlength@?$vector@V?$tuple@NN@std@@V?$allocator@V?$tuple@NN@std@@@2@@std@@CAXXZ
	?_Xlength@?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@CAXXZ
	?_Xlength@?$vector@V?$vector@HV?$allocator@H@std@@@std@@V?$allocator@V?$vector@HV?$allocator@H@std@@@std@@@2@@std@@CAXXZ
	?_Xlength@?$vector@V?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@V?$allocator@V?$vector@V?$tuple@NNN@std@@V?$allocator@V?$tuple@NNN@std@@@2@@std@@@2@@std@@CAXXZ
	?_Xlength@?$vector@VDTMDrapedLinearElementPoint@TerrainModel@BentleyM0200@@V?$allocator@VDTMDrapedLinearElementPoint@TerrainModel@BentleyM0200@@@std@@@std@@CAXXZ
	?_Xlength@?$vector@VDTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@V?$allocator@VDTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@@std@@@std@@CAXXZ
	?_Xlength@?$vector@VDTMFeature@TerrainModel@BentleyM0200@@V?$allocator@VDTMFeature@TerrainModel@BentleyM0200@@@std@@@std@@CAXXZ
	?_Xlength@?$vector@VDTMMesh@TerrainModel@BentleyM0200@@V?$allocator@VDTMMesh@TerrainModel@BentleyM0200@@@std@@@std@@CAXXZ
	?_Xlength@?$vector@VDTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@V?$allocator@VDTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@@std@@@std@@CAXXZ
	?_Xlength@?$vector@VVolumePolygon@TerrainModel@BentleyM0200@@V?$allocator@VVolumePolygon@TerrainModel@BentleyM0200@@@std@@@std@@CAXXZ
	?_Xlength@?$vector@_JV?$allocator@_J@std@@@std@@CAXXZ
	?_Xran@?$_String_val@U?$_Simple_types@D@std@@@std@@SAXXZ
	?_Xrange@?$vector@VDTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@V?$allocator@VDTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@@std@@@std@@CAXXZ
	?__empty_global_delete@@YAXPEAX@Z
	?__empty_global_delete@@YAXPEAX_K@Z
	?begin@DTMFeatureEnumerator@TerrainModel@BentleyM0200@@QEAA?AVIterator@123@XZ
	?begin@DTMMeshEnumerator@TerrainModel@BentleyM0200@@QEAA?AVIterator@123@XZ
	?clear@?$_Hash@V?$_Umap_traits@W4DTMFeatureType@TerrainModel@BentleyM0200@@_NV?$_Uhash_compare@W4DTMFeatureType@TerrainModel@BentleyM0200@@U?$hash@W4DTMFeatureType@TerrainModel@BentleyM0200@@@std@@U?$equal_to@W4DTMFeatureType@TerrainModel@BentleyM0200@@@5@@std@@V?$allocator@U?$pair@$$CBW4DTMFeatureType@TerrainModel@BentleyM0200@@_N@std@@@5@$0A@@std@@@std@@QEAAXXZ
	?close@?$basic_filebuf@DU?$char_traits@D@std@@@std@@QEAAPEAV12@XZ
	?deallocate@?$allocator@D@std@@QEAAXQEAD_K@Z
	?deallocate@?$allocator@UDPoint3d@BentleyM0200@@@std@@QEAAXQEAUDPoint3d@BentleyM0200@@_K@Z
	?deallocate@?$allocator@UDTMDrapePointFeature@TerrainModel@BentleyM0200@@@std@@QEAAXQEAUDTMDrapePointFeature@TerrainModel@BentleyM0200@@_K@Z
	?deallocate@?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBW4DTMFeatureType@TerrainModel@BentleyM0200@@_N@std@@@std@@@std@@@std@@@std@@QEAAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBW4DTMFeatureType@TerrainModel@BentleyM0200@@_N@std@@@std@@@std@@@2@_K@Z
	?deallocate@?$allocator@V?$tuple@HHH@std@@@std@@QEAAXQEAV?$tuple@HHH@2@_K@Z
	?deallocate@?$allocator@V?$tuple@NN@std@@@std@@QEAAXQEAV?$tuple@NN@2@_K@Z
	?deallocate@?$allocator@V?$tuple@NNN@std@@@std@@QEAAXQEAV?$tuple@NNN@2@_K@Z
	?deallocate@?$allocator@VDTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@@std@@QEAAXQEAVDTMDynamicFeatureInfo@TerrainModel@BentleyM0200@@_K@Z
	?deallocate@?$allocator@VDTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@@std@@QEAAXQEAVDTMSideSlopeInputPoint@TerrainModel@BentleyM0200@@_K@Z
	?deallocate@?$allocator@VVolumePolygon@TerrainModel@BentleyM0200@@@std@@QEAAXQEAVVolumePolygon@TerrainModel@BentleyM0200@@_K@Z
	?deallocate@?$allocator@_J@std@@QEAAXQEA_J_K@Z
	?end@DTMFeatureEnumerator@TerrainModel@BentleyM0200@@QEAA?AVIterator@123@XZ
	?end@DTMMeshEnumerator@TerrainModel@BentleyM0200@@QEAA?AVIterator@123@XZ
	?erase@?$vector@_NV?$allocator@_N@std@@@std@@QEAA?AV?$_Vb_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@V?$_Vb_const_iterator@U?$_Wrap_alloc@V?$allocator@I@std@@@std@@@2@0@Z
	?imbue@?$basic_filebuf@DU?$char_traits@D@std@@@std@@MEAAXAEBVlocale@2@@Z
	?open@?$basic_filebuf@DU?$char_traits@D@std@@@std@@QEAAPEAV12@PEBDHH@Z
	?overflow@?$basic_filebuf@DU?$char_traits@D@std@@@std@@MEAAHH@Z
	?overflow@?$basic_stringbuf@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@MEAAHH@Z
	?pbackfail@?$basic_filebuf@DU?$char_traits@D@std@@@std@@MEAAHH@Z
	?pbackfail@?$basic_stringbuf@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@MEAAHH@Z
	?push_back@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAXD@Z
	?resize@?$vector@_NV?$allocator@_N@std@@@std@@QEAAX_K_N@Z
	?seekoff@?$basic_filebuf@DU?$char_traits@D@std@@@std@@MEAA?AV?$fpos@U_Mbstatet@@@2@_JHH@Z
	?seekoff@?$basic_stringbuf@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@MEAA?AV?$fpos@U_Mbstatet@@@2@_JHH@Z
	?seekpos@?$basic_filebuf@DU?$char_traits@D@std@@@std@@MEAA?AV?$fpos@U_Mbstatet@@@2@V32@H@Z
	?seekpos@?$basic_stringbuf@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@MEAA?AV?$fpos@U_Mbstatet@@@2@V32@H@Z
	?setbuf@?$basic_filebuf@DU?$char_traits@D@std@@@std@@MEAAPEAV?$basic_streambuf@DU?$char_traits@D@std@@@2@PEAD_J@Z
	?sync@?$basic_filebuf@DU?$char_traits@D@std@@@std@@MEAAHXZ
	?uflow@?$basic_filebuf@DU?$char_traits@D@std@@@std@@MEAAHXZ
	?underflow@?$basic_filebuf@DU?$char_traits@D@std@@@std@@MEAAHXZ
	?underflow@?$basic_stringbuf@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@MEAAHXZ
	?what@exception@std@@UEBAPEBDXZ
	?xsgetn@?$basic_filebuf@DU?$char_traits@D@std@@@std@@MEAA_JPEAD_J@Z
	?xsputn@?$basic_filebuf@DU?$char_traits@D@std@@@std@@MEAA_JPEBD_J@Z
