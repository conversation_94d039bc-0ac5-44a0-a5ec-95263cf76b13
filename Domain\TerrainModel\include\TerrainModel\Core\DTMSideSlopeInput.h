/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See LICENSE.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModel/TerrainModel.h"
#include "TerrainModel/Core/DTMSideSlopeInputPoint.h"
#include <vector>
#include <memory>

BEGIN_BENTLEY_TERRAINMODEL_NAMESPACE

// 此文件是重构过程中的占位符
// 将在实现阶段添加实际代码

// 前向声明
class DTM;
class DTMSideSlopeInputImpl;

/*=================================================================================**//**
* @brief 坡度类型枚举
* @details 定义了坡度的不同计算方式
* @bsiclass
+===============+===============+===============+===============+===============+======*/
enum class SlopeType
{
    Ratio = 0,      //!< 比例坡度 (1:n)
    Percent = 1,    //!< 百分比坡度 (n%)
    Degree = 2      //!< 角度坡度 (n°)
};

/*=================================================================================**//**
* @brief 边坡输入类
* @details 用于定义DTM边坡计算的输入参数
* @bsiclass
+===============+===============+===============+===============+===============+======*/
class TERRAINMODEL_EXPORT DTMSideSlopeInput
{
private:
    std::unique_ptr<DTMSideSlopeInputImpl> m_impl;

public:
    //! 默认构造函数
    DTMSideSlopeInput();
    
    //! 析构函数
    ~DTMSideSlopeInput();
    
    //! 拷贝构造函数
    DTMSideSlopeInput(const DTMSideSlopeInput& other);
    
    //! 移动构造函数
    DTMSideSlopeInput(DTMSideSlopeInput&& other) noexcept;
    
    //! 拷贝赋值运算符
    DTMSideSlopeInput& operator=(const DTMSideSlopeInput& other);
    
    //! 移动赋值运算符
    DTMSideSlopeInput& operator=(DTMSideSlopeInput&& other) noexcept;

    //=======================================================================================
    //! @name 基本属性
    //! @{
    
    //! 设置坡度类型
    //! @param[in] type 坡度类型
    void SetSlopeType(SlopeType type);
    
    //! 获取坡度类型
    //! @return 坡度类型
    SlopeType GetSlopeType() const;
    
    //! 设置内侧坡度值
    //! @param[in] value 坡度值
    //! @details 根据坡度类型解释：比例(1:n)、百分比(n%)或角度(n°)
    void SetInsideSlope(double value);
    
    //! 获取内侧坡度值
    //! @return 内侧坡度值
    double GetInsideSlope() const;
    
    //! 设置外侧坡度值
    //! @param[in] value 坡度值
    void SetOutsideSlope(double value);
    
    //! 获取外侧坡度值
    //! @return 外侧坡度值
    double GetOutsideSlope() const;
    
    //! @}
    
    //=======================================================================================
    //! @name 输入点操作
    //! @{
    
    //! 添加输入点
    //! @param[in] point 输入点
    //! @return 添加的点索引
    int AddPoint(const DTMSideSlopeInputPoint& point);
    
    //! 添加输入点
    //! @param[in] x X坐标
    //! @param[in] y Y坐标
    //! @param[in] z Z坐标
    //! @param[in] width 宽度
    //! @return 添加的点索引
    int AddPoint(double x, double y, double z, double width);
    
    //! 获取输入点
    //! @param[in] index 点索引
    //! @return 输入点引用
    const DTMSideSlopeInputPoint& GetPoint(int index) const;
    
    //! 获取输入点数量
    //! @return 点数量
    int GetPointCount() const;
    
    //! 清空所有输入点
    void ClearPoints();
    
    //! @}
    
    //=======================================================================================
    //! @name 边坡计算
    //! @{
    
    //! 计算边坡
    //! @param[in] dtm 地形模型
    //! @param[out] cutVolume 挖方体积
    //! @param[out] fillVolume 填方体积
    //! @return 操作是否成功
    bool CalculateSideSlope(const DTM& dtm, double& cutVolume, double& fillVolume);
    
    //! 导出边坡线
    //! @param[out] crestLines 顶部边坡线点集
    //! @param[out] toeLines 底部边坡线点集
    //! @return 操作是否成功
    bool ExportSlopeLines(std::vector<std::vector<std::tuple<double, double, double>>>& crestLines,
                         std::vector<std::vector<std::tuple<double, double, double>>>& toeLines);
    
    //! @}
};

END_BENTLEY_TERRAINMODEL_NAMESPACE
