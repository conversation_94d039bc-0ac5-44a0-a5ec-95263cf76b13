﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{6A730DC1-53D2-34A9-8849-BDCA1FEA158D}"
	ProjectSection(ProjectDependencies) = postProject
		{6ED63E85-1562-3683-88F9-3773AFAC31F9} = {6ED63E85-1562-3683-88F9-3773AFAC31F9}
		{40B1800F-E786-3125-87A1-0EC756D3C89D} = {40B1800F-E786-3125-87A1-0EC756D3C89D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{EDCF2190-5B72-3572-A343-0EFC763E03A<PERSON>}"
	ProjectSection(ProjectDependencies) = postProject
		{6A730DC1-53D2-34A9-8849-BDCA1FEA158D} = {6A730DC1-53D2-34A9-8849-BDCA1FEA158D}
		{40B1800F-E786-3125-87A1-0EC756D3C89D} = {40B1800F-E786-3125-87A1-0EC756D3C89D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "TerrainModel", "TerrainModel\TerrainModel.vcxproj", "{6ED63E85-1562-3683-88F9-3773AFAC31F9}"
	ProjectSection(ProjectDependencies) = postProject
		{40B1800F-E786-3125-87A1-0EC756D3C89D} = {40B1800F-E786-3125-87A1-0EC756D3C89D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\\ZERO_CHECK.vcxproj", "{40B1800F-E786-3125-87A1-0EC756D3C89D}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{6A730DC1-53D2-34A9-8849-BDCA1FEA158D}.Debug|x64.ActiveCfg = Debug|x64
		{6A730DC1-53D2-34A9-8849-BDCA1FEA158D}.Debug|x64.Build.0 = Debug|x64
		{6A730DC1-53D2-34A9-8849-BDCA1FEA158D}.Release|x64.ActiveCfg = Release|x64
		{6A730DC1-53D2-34A9-8849-BDCA1FEA158D}.Release|x64.Build.0 = Release|x64
		{6A730DC1-53D2-34A9-8849-BDCA1FEA158D}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{6A730DC1-53D2-34A9-8849-BDCA1FEA158D}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{6A730DC1-53D2-34A9-8849-BDCA1FEA158D}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{6A730DC1-53D2-34A9-8849-BDCA1FEA158D}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{EDCF2190-5B72-3572-A343-0EFC763E03AE}.Debug|x64.ActiveCfg = Debug|x64
		{EDCF2190-5B72-3572-A343-0EFC763E03AE}.Release|x64.ActiveCfg = Release|x64
		{EDCF2190-5B72-3572-A343-0EFC763E03AE}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{EDCF2190-5B72-3572-A343-0EFC763E03AE}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{6ED63E85-1562-3683-88F9-3773AFAC31F9}.Debug|x64.ActiveCfg = Debug|x64
		{6ED63E85-1562-3683-88F9-3773AFAC31F9}.Debug|x64.Build.0 = Debug|x64
		{6ED63E85-1562-3683-88F9-3773AFAC31F9}.Release|x64.ActiveCfg = Release|x64
		{6ED63E85-1562-3683-88F9-3773AFAC31F9}.Release|x64.Build.0 = Release|x64
		{6ED63E85-1562-3683-88F9-3773AFAC31F9}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{6ED63E85-1562-3683-88F9-3773AFAC31F9}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{6ED63E85-1562-3683-88F9-3773AFAC31F9}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{6ED63E85-1562-3683-88F9-3773AFAC31F9}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{40B1800F-E786-3125-87A1-0EC756D3C89D}.Debug|x64.ActiveCfg = Debug|x64
		{40B1800F-E786-3125-87A1-0EC756D3C89D}.Debug|x64.Build.0 = Debug|x64
		{40B1800F-E786-3125-87A1-0EC756D3C89D}.Release|x64.ActiveCfg = Release|x64
		{40B1800F-E786-3125-87A1-0EC756D3C89D}.Release|x64.Build.0 = Release|x64
		{40B1800F-E786-3125-87A1-0EC756D3C89D}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{40B1800F-E786-3125-87A1-0EC756D3C89D}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{40B1800F-E786-3125-87A1-0EC756D3C89D}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{40B1800F-E786-3125-87A1-0EC756D3C89D}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {9495C02A-3F2A-31B0-9319-8882F8B4CD98}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
