/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See LICENSE.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#include "TerrainModel/Core/DTMHelpers.h"
#include "TerrainModel/Core/DTM.h"
#include "TerrainModel/Core/DTMFeature.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <cmath>
#include <map>
#include <unordered_map>
#include <Geom/GeomApi.h>

BEGIN_BENTLEY_TERRAINMODEL_NAMESPACE

// 常量定义
#ifndef PI
#define PI 3.14159265358979323846
#endif



// 文件操作
DTMFileFormat DTMHelpers::DetectFileFormat(const std::string& fileName)
{
    // 获取文件扩展名
    std::string ext;
    size_t dotPos = fileName.find_last_of('.');
    if (dotPos != std::string::npos)
        ext = fileName.substr(dotPos);
    
    // 转换为小写
    std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
    
    // 根据扩展名判断
    if (ext == ".xyz" || ext == ".pts" || ext == ".txt")
        return DTMFileFormat::XYZ;
    else if (ext == ".tin")
        return DTMFileFormat::TIN;
    else if (ext == ".dxf")
        return DTMFileFormat::DXF;
    else if (ext == ".xml")
        return DTMFileFormat::LandXML;
    else if (ext == ".shp")
        return DTMFileFormat::Shapefile;
    else if (ext == ".tif" || ext == ".tiff")
        return DTMFileFormat::GeoTIFF;
    else if (ext == ".csv")
        return DTMFileFormat::CSV;
    
    // 如果扩展名无法判断，尝试读取文件内容判断
    // 此处仅为简单实现，实际应检查文件头或内容格式
    
    return DTMFileFormat::Unknown;
}

bool DTMHelpers::ReadXYZFile(const std::string& fileName, std::vector<std::tuple<double, double, double>>& points)
{
    try
    {
        // 清空输出
        points.clear();
        
        // 打开文件
        std::ifstream file(fileName);
        if (!file.is_open())
            return false;
        
        std::string line;
        while (std::getline(file, line))
        {
            // 跳过空行
            if (line.empty())
                continue;
            
            // 跳过注释行
            if (line[0] == '#' || line[0] == '/')
                continue;
            
            std::istringstream iss(line);
            double x, y, z;
            
            // 尝试读取三个数值
            if (iss >> x >> y >> z)
            {
                points.push_back(std::make_tuple(x, y, z));
            }
        }
        
        file.close();
        return !points.empty();
    }
    catch (...)
    {
        return false;
    }
}

bool DTMHelpers::WriteXYZFile(const std::string& fileName, const std::vector<std::tuple<double, double, double>>& points)
{
    try
    {
        // 打开文件
        std::ofstream file(fileName);
        if (!file.is_open())
            return false;
        
        // 写入点坐标
        for (const auto& point : points)
        {
            double x = std::get<0>(point);
            double y = std::get<1>(point);
            double z = std::get<2>(point);
            
            file << x << " " << y << " " << z << std::endl;
        }
        
        file.close();
        return true;
    }
    catch (...)
    {
        return false;
    }
}

// 坐标转换
bool DTMHelpers::LonLatToXY(double lon, double lat, double& x, double& y, int epsgCode)
{
    // 简单实现 - 实际应使用投影库如PROJ.4或GDAL
    // Web墨卡托投影 (EPSG:3857) 简化计算
    if (epsgCode == 3857)
    {
        const double EARTH_RADIUS = 6378137.0; // 地球半径
        const double DEG_TO_RAD = PI / 180.0;
        
        x = EARTH_RADIUS * lon * DEG_TO_RAD;
        y = EARTH_RADIUS * std::log(std::tan(PI / 4.0 + lat * DEG_TO_RAD / 2.0));
        
        return true;
    }
    
    // 对于其他投影代码，此处仅为占位实现
    // 实际应使用完整的坐标转换库
    return false;
}

bool DTMHelpers::XYToLonLat(double x, double y, double& lon, double& lat, int epsgCode)
{
    // 简单实现 - 实际应使用投影库如PROJ.4或GDAL
    // Web墨卡托投影 (EPSG:3857) 简化计算
    if (epsgCode == 3857)
    {
        const double EARTH_RADIUS = 6378137.0; // 地球半径
        const double RAD_TO_DEG = 180.0 / PI;
        
        lon = x / EARTH_RADIUS * RAD_TO_DEG;
        lat = (2.0 * std::atan(std::exp(y / EARTH_RADIUS)) - PI / 2.0) * RAD_TO_DEG;
        
        return true;
    }
    
    // 对于其他投影代码，此处仅为占位实现
    // 实际应使用完整的坐标转换库
    return false;
}

// 地形分析
bool DTMHelpers::CalculateSteepestPath(const DTM& dtm, double startX, double startY, 
                                     std::vector<std::tuple<double, double, double>>& path,
                                     int maxSteps, double stepSize)
{
    // 清空路径
    path.clear();
    
    // 验证参数
    if (maxSteps <= 0 || stepSize <= 0.0)
        return false;
    
    // 获取起点高程
    double startZ;
    if (!dtm.GetElevationAtPoint(startX, startY, startZ))
        return false;
    
    // 添加起点
    path.push_back(std::make_tuple(startX, startY, startZ));
    
    // 当前位置
    double currentX = startX;
    double currentY = startY;
    double currentZ = startZ;
    
    // 迭代寻找最陡路径
    for (int step = 0; step < maxSteps; ++step)
    {
        // 探索8个方向
        const int DIRECTIONS = 8;
        const double ANGLES[DIRECTIONS] = {
            0.0, 
            PI / 4.0, 
            PI / 2.0, 
            3.0 * PI / 4.0, 
            PI, 
            5.0 * PI / 4.0, 
            3.0 * PI / 2.0, 
            7.0 * PI / 4.0
        };
        
        double maxSlope = 0.0;
        double nextX = currentX;
        double nextY = currentY;
        double nextZ = currentZ;
        bool foundSteeper = false;
        
        // 在每个方向上检查坡度
        for (int dir = 0; dir < DIRECTIONS; ++dir)
        {
            double angle = ANGLES[dir];
            double testX = currentX + stepSize * std::cos(angle);
            double testY = currentY + stepSize * std::sin(angle);
            double testZ;
            
            // 获取测试点高程
            if (!dtm.GetElevationAtPoint(testX, testY, testZ))
                continue;
            
            // 计算坡度 (负值表示下坡)
            double slope = (currentZ - testZ) / stepSize;
            
            // 寻找最陡下坡
            if (slope > maxSlope)
            {
                maxSlope = slope;
                nextX = testX;
                nextY = testY;
                nextZ = testZ;
                foundSteeper = true;
            }
        }
        
        // 如果找不到更陡的路径，结束搜索
        if (!foundSteeper || std::abs(maxSlope) < 0.001) // 坡度接近0
            break;
        
        // 移动到下一点
        currentX = nextX;
        currentY = nextY;
        currentZ = nextZ;
        
        // 添加到路径
        path.push_back(std::make_tuple(currentX, currentY, currentZ));
    }
    
    return !path.empty();
}

bool DTMHelpers::PerformLineOfSightAnalysis(const DTM& dtm, 
                                          double x1, double y1, double z1,
                                          double x2, double y2, double z2,
                                          bool& isVisible,
                                          std::vector<std::tuple<double, double, double>>& obstaclePoints)
{
    // 清空输出
    obstaclePoints.clear();
    isVisible = true;
    
    // 获取地面高程
    double groundZ1, groundZ2;
    if (!dtm.GetElevationAtPoint(x1, y1, groundZ1) || !dtm.GetElevationAtPoint(x2, y2, groundZ2))
        return false;
    
    // 添加高度偏移
    groundZ1 += z1;
    groundZ2 += z2;
    
    // 计算两点距离
    double distance = std::sqrt(std::pow(x2 - x1, 2) + std::pow(y2 - y1, 2));
    
    // 如果距离过小，直接返回可见
    if (distance < 0.1)
    {
        isVisible = true;
        return true;
    }
    
    // 设置采样步长
    double stepSize = std::min(1.0, distance / 100.0); // 最小步长1米，或者距离的1/100
    int steps = static_cast<int>(distance / stepSize) + 1;
    
    // 计算方向向量
    double dx = (x2 - x1) / distance;
    double dy = (y2 - y1) / distance;
    
    // 计算视线高度变化率
    double dz = (groundZ2 - groundZ1) / distance;
    
    // 检查沿线的每个点
    for (int i = 1; i < steps; ++i)
    {
        // 计算当前点位置
        double ratio = i * stepSize / distance;
        double currentX = x1 + ratio * (x2 - x1);
        double currentY = y1 + ratio * (y2 - y1);
        
        // 当前视线高度
        double sightlineZ = groundZ1 + ratio * (groundZ2 - groundZ1);
        
        // 获取当前点地面高程
        double groundZ;
        if (!dtm.GetElevationAtPoint(currentX, currentY, groundZ))
            continue;
        
        // 检查地面是否高于视线
        if (groundZ > sightlineZ)
        {
            // 记录障碍点
            obstaclePoints.push_back(std::make_tuple(currentX, currentY, groundZ));
            isVisible = false;
            
            // 可选: 找到第一个障碍点后立即返回
            // 如果需要找出所有障碍点，则删除以下行
            // break;
        }
    }
    
    return true;
}

bool DTMHelpers::PerformWatershedAnalysis(const DTM& dtm,
                                        double pourPointX, double pourPointY,
                                        std::vector<std::tuple<double, double>>& watershedBoundary,
                                        std::vector<std::vector<std::tuple<double, double, double>>>& streamLines)
{
    // 清空输出
    watershedBoundary.clear();
    streamLines.clear();
    
    // 此方法需要复杂的水文分析算法
    // 简单实现返回一个矩形流域和一条简单河流
    
    // 获取DTM边界
    double minX, minY, minZ, maxX, maxY, maxZ;
    if (!dtm.GetExtents(minX, minY, minZ, maxX, maxY, maxZ))
        return false;
    
    // 创建简化的流域边界（矩形）
    double size = std::min(maxX - minX, maxY - minY) / 5.0;
    watershedBoundary.push_back(std::make_tuple(pourPointX - size, pourPointY - size));
    watershedBoundary.push_back(std::make_tuple(pourPointX + size, pourPointY - size));
    watershedBoundary.push_back(std::make_tuple(pourPointX + size, pourPointY + size));
    watershedBoundary.push_back(std::make_tuple(pourPointX - size, pourPointY + size));
    watershedBoundary.push_back(std::make_tuple(pourPointX - size, pourPointY - size)); // 闭合
    
    // 创建一条简化的河流线
    std::vector<std::tuple<double, double, double>> streamLine;
    
    // 获取汇水点高程
    double pourPointZ;
    if (!dtm.GetElevationAtPoint(pourPointX, pourPointY, pourPointZ))
        return false;
    
    // 从汇水点开始计算最陡路径
    if (CalculateSteepestPath(dtm, pourPointX, pourPointY, streamLine))
    {
        streamLines.push_back(streamLine);
    }
    
    return true;
}

// 点云处理
bool DTMHelpers::DownsamplePointCloud(const std::vector<std::tuple<double, double, double>>& points,
                                   std::vector<std::tuple<double, double, double>>& sampledPoints,
                                   double gridSize)
{
    // 验证参数
    if (gridSize <= 0.0 || points.empty())
        return false;
    
    // 清空输出
    sampledPoints.clear();
    
    // 创建网格哈希表
    std::map<std::pair<int, int>, std::vector<std::tuple<double, double, double>>> gridCells;
    
    // 将点分配到网格单元
    for (const auto& point : points)
    {
        double x = std::get<0>(point);
        double y = std::get<1>(point);
        
        // 计算网格索引
        int gridX = static_cast<int>(std::floor(x / gridSize));
        int gridY = static_cast<int>(std::floor(y / gridSize));
        
        // 添加到相应网格单元
        gridCells[std::make_pair(gridX, gridY)].push_back(point);
    }
    
    // 从每个网格单元选择一个点（这里简单取平均值）
    for (const auto& cell : gridCells)
    {
        const auto& cellPoints = cell.second;
        
        // 计算平均点
        double sumX = 0.0, sumY = 0.0, sumZ = 0.0;
        for (const auto& point : cellPoints)
        {
            sumX += std::get<0>(point);
            sumY += std::get<1>(point);
            sumZ += std::get<2>(point);
        }
        
        double avgX = sumX / cellPoints.size();
        double avgY = sumY / cellPoints.size();
        double avgZ = sumZ / cellPoints.size();
        
        // 添加到结果
        sampledPoints.push_back(std::make_tuple(avgX, avgY, avgZ));
    }
    
    return !sampledPoints.empty();
}

bool DTMHelpers::FilterPointCloud(const std::vector<std::tuple<double, double, double>>& points,
                               std::vector<std::tuple<double, double, double>>& filteredPoints,
                               double stdDevThreshold,
                               int neighborhoodSize)
{
    // 验证参数
    if (points.empty() || stdDevThreshold <= 0.0 || neighborhoodSize <= 0)
        return false;
    
    // 清空输出
    filteredPoints.clear();
    
    // 如果点数太少，直接返回原始点
    if (points.size() < static_cast<size_t>(neighborhoodSize + 1))
    {
        filteredPoints = points;
        return true;
    }
    
    // 对每个点执行统计异常值检测
    for (size_t i = 0; i < points.size(); ++i)
    {
        const auto& point = points[i];
        double x = std::get<0>(point);
        double y = std::get<1>(point);
        double z = std::get<2>(point);
        
        // 收集邻近点
        std::vector<double> neighborZ;
        
        // 简单实现：使用前neighborhoodSize个点作为邻居
        // 实际应根据空间距离选择邻居
        for (size_t j = 0; j < points.size() && neighborZ.size() < static_cast<size_t>(neighborhoodSize); ++j)
        {
            if (i == j)
                continue;
                
            double neighborX = std::get<0>(points[j]);
            double neighborY = std::get<1>(points[j]);
            
            // 计算平面距离
            double dist = std::sqrt(std::pow(x - neighborX, 2) + std::pow(y - neighborY, 2));
            
            // 距离阈值（简化实现）
            if (dist < 10.0) // 使用固定的距离阈值
            {
                neighborZ.push_back(std::get<2>(points[j]));
            }
        }
        
        // 如果邻居太少，保留该点
        if (neighborZ.size() < 3)
        {
            filteredPoints.push_back(point);
            continue;
        }
        
        // 计算邻域Z值的平均值和标准差
        double sum = 0.0;
        for (double zVal : neighborZ)
        {
            sum += zVal;
        }
        double mean = sum / neighborZ.size();
        
        double sumSqDiff = 0.0;
        for (double zVal : neighborZ)
        {
            sumSqDiff += std::pow(zVal - mean, 2);
        }
        double stdDev = std::sqrt(sumSqDiff / neighborZ.size());
        
        // 检查点是否为异常值
        if (std::abs(z - mean) <= stdDevThreshold * stdDev)
        {
            // 保留非异常点
            filteredPoints.push_back(point);
        }
    }
    
    return !filteredPoints.empty();
}

END_BENTLEY_TERRAINMODEL_NAMESPACE

