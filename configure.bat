@echo off
setlocal enabledelayedexpansion

:: 脚本路径和项目根目录
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%"
cd "%PROJECT_ROOT%"

:: 创建并进入构建目录
if not exist build mkdir build
cd build

:: 设置SDK路径（根据实际情况修改）
set "SDK_ROOT=D:/sdk/native"

:: 配置选项
set "CMAKE_OPTIONS=-DCMAKE_BUILD_TYPE=Release"
set "CMAKE_OPTIONS=%CMAKE_OPTIONS% -DSDK_ROOT=%SDK_ROOT%"

:: 生成Visual Studio解决方案
echo 正在配置项目...
cmake %CMAKE_OPTIONS% -G "Visual Studio 17 2022" -A x64 ..

if %ERRORLEVEL% neq 0 (
    echo 配置失败，错误代码: %ERRORLEVEL%
    exit /b %ERRORLEVEL%
) else (
    echo 配置成功! 解决方案文件位于: %PROJECT_ROOT%build
)

:: 返回项目根目录
cd "%PROJECT_ROOT%" 