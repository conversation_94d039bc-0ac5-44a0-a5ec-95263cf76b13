@PACKAGE_INIT@

include(CMakeFindDependencyMacro)

# 查找依赖项
find_dependency(Threads REQUIRED)

# 包含目标文件
include("${CMAKE_CURRENT_LIST_DIR}/TerrainModelTargets.cmake")

# 检查组件
check_required_components(TerrainModel)

# 设置变量
set(TerrainModel_FOUND TRUE)
set(TerrainModel_VERSION @PROJECT_VERSION@)
set(TerrainModel_INCLUDE_DIRS "@PACKAGE_CMAKE_INSTALL_INCLUDEDIR@")
set(TerrainModel_LIBRARIES TerrainModel::TerrainModel)

# 提供向后兼容的变量名
set(TERRAINMODEL_FOUND ${TerrainModel_FOUND})
set(TERRAINMODEL_VERSION ${TerrainModel_VERSION})
set(TERRAINMODEL_INCLUDE_DIRS ${TerrainModel_INCLUDE_DIRS})
set(TERRAINMODEL_LIBRARIES ${TerrainModel_LIBRARIES})
