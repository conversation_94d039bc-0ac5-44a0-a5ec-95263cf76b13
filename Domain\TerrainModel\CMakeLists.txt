cmake_minimum_required(VERSION 3.14)

project(TerrainModel
    VERSION 1.0.0
    LANGUAGES CXX
    DESCRIPTION "Terrain Model Core Library"
)

# 设置C++标准
set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 预处理器定义
add_definitions(
    -D__TERRAINMODEL_BUILD__
    -DBENTLEY_WIN32
    -D_ITERATOR_DEBUG_LEVEL=0
)

# 创建源文件目录结构和占位符文件
file(MAKE_DIRECTORY 
    ${CMAKE_CURRENT_SOURCE_DIR}/src/Core
    ${CMAKE_CURRENT_SOURCE_DIR}/src/Drainage
    ${CMAKE_CURRENT_SOURCE_DIR}/include/TerrainModel
    ${CMAKE_CURRENT_SOURCE_DIR}/include/TerrainModel/Core
    ${CMAKE_CURRENT_SOURCE_DIR}/include/TerrainModel/Drainage
)

# 定义路径
set(CORE_SRC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/src/Core)
set(DRAINAGE_SRC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/src/Drainage)
set(CORE_INCLUDE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/include/TerrainModel/Core)
set(DRAINAGE_INCLUDE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/include/TerrainModel/Drainage)
set(MAIN_INCLUDE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/include/TerrainModel)

# 确保文件存在，如果不存在，创建占位符文件
function(ensure_file_exists file content)
    if(NOT EXISTS ${file})
        file(WRITE ${file} "${content}")
    endif()
endfunction()

# 为每个源文件创建占位符，如果它们尚不存在
set(CPP_PLACEHOLDERS
    "${CORE_SRC_DIR}/DTMFeature.cpp"
    "${CORE_SRC_DIR}/DTMMesh.cpp"
    "${CORE_SRC_DIR}/DTMPond.cpp"
    "${CORE_SRC_DIR}/DTMTinEditor.cpp"
    "${CORE_SRC_DIR}/DTMHelpers.cpp"
    "${CORE_SRC_DIR}/DTMSideSlopeInput.cpp"
    "${CORE_SRC_DIR}/DTMSideSlopeInputPoint.cpp"
    "${CORE_SRC_DIR}/DTMDrapedLinearElement.cpp"
    "${CORE_SRC_DIR}/Caching.cpp"
    "${CORE_SRC_DIR}/DTMFeatureEnumerator.cpp"
    "${CORE_SRC_DIR}/DTMMeshEnumerator.cpp"
    "${DRAINAGE_SRC_DIR}/WaterAnalysis.cpp"
)

# 为每个头文件创建占位符，如果它们尚不存在
set(H_PLACEHOLDERS
    "${CORE_INCLUDE_DIR}/DTMMesh.h"
    "${CORE_INCLUDE_DIR}/DTMPond.h"
    "${CORE_INCLUDE_DIR}/DTMTinEditor.h"
    "${CORE_INCLUDE_DIR}/DTMHelpers.h"
    "${CORE_INCLUDE_DIR}/DTMSideSlopeInput.h"
    "${CORE_INCLUDE_DIR}/DTMSideSlopeInputPoint.h"
    "${CORE_INCLUDE_DIR}/DTMDrapedLinearElement.h"
    "${CORE_INCLUDE_DIR}/Caching.h"
    "${CORE_INCLUDE_DIR}/DTMFeatureEnumerator.h"
    "${CORE_INCLUDE_DIR}/DTMMeshEnumerator.h"
    "${DRAINAGE_INCLUDE_DIR}/WaterAnalysis.h"
)

set(CPP_PLACEHOLDER_CONTENT "/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See LICENSE.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/

// 此文件是重构过程中的占位符
// 将在实现阶段添加实际代码

")

set(H_PLACEHOLDER_CONTENT "/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See LICENSE.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include \"TerrainModel/TerrainModel.h\"

BEGIN_BENTLEY_TERRAINMODEL_NAMESPACE

// 此文件是重构过程中的占位符
// 将在实现阶段添加实际代码

END_BENTLEY_TERRAINMODEL_NAMESPACE
")

# 创建所有源文件占位符
foreach(PLACEHOLDER ${CPP_PLACEHOLDERS})
    ensure_file_exists(${PLACEHOLDER} "${CPP_PLACEHOLDER_CONTENT}")
endforeach()

# 创建所有头文件占位符
foreach(PLACEHOLDER ${H_PLACEHOLDERS})
    ensure_file_exists(${PLACEHOLDER} "${H_PLACEHOLDER_CONTENT}")
endforeach()

# 源文件列表
set(SOURCES
    src/Core/DTM.cpp
    src/Core/DTMFeature.cpp
    src/Core/DTMMesh.cpp
    src/Core/DTMPond.cpp
    src/Core/DTMTinEditor.cpp
    src/Core/DTMHelpers.cpp
    src/Core/DTMSideSlopeInput.cpp
    src/Core/DTMSideSlopeInputPoint.cpp
    src/Core/DTMDrapedLinearElement.cpp
    src/Core/Caching.cpp
    src/Core/DTMFeatureEnumerator.cpp
    src/Core/DTMMeshEnumerator.cpp
    src/Drainage/WaterAnalysis.cpp
)

# 头文件列表
set(HEADERS
    include/TerrainModel/TerrainModel.h
    include/TerrainModel/Core/DTM.h
    include/TerrainModel/Core/DTMFeature.h
    include/TerrainModel/Core/DTMMesh.h
    include/TerrainModel/Core/DTMPond.h
    include/TerrainModel/Core/DTMTinEditor.h
    include/TerrainModel/Core/DTMHelpers.h
    include/TerrainModel/Core/DTMSideSlopeInput.h
    include/TerrainModel/Core/DTMSideSlopeInputPoint.h
    include/TerrainModel/Core/DTMDrapedLinearElement.h
    include/TerrainModel/Core/DTMDelegates.h
    include/TerrainModel/Core/DTMException.h
    include/TerrainModel/Core/Caching.h
    include/TerrainModel/Core/DTMFeatureEnumerator.h
    include/TerrainModel/Core/DTMMeshEnumerator.h
    include/TerrainModel/Drainage/WaterAnalysis.h
)

# 创建库
add_library(TerrainModel SHARED ${SOURCES} ${HEADERS})

# 包含目录
target_include_directories(TerrainModel
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
        $<INSTALL_INTERFACE:include>
        ${SDK_INCLUDE_DIR}
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/src
)

# 编译器选项和定义
if(MSVC)
    target_compile_options(TerrainModel PRIVATE 
        /W4
        /wd4251  # 类需要dll导出接口警告禁用
        /wd4275  # dll导出类非dll导出类的派生警告禁用
    )
    
    # MSVC导出宏定义
    target_compile_definitions(TerrainModel PRIVATE
        TERRAINMODEL_EXPORT=__declspec\(dllexport\)
    )
else()
    target_compile_options(TerrainModel PRIVATE 
        -Wall 
        -Wextra
    )
    
    # GCC/Clang导出宏定义
    target_compile_definitions(TerrainModel PRIVATE
        TERRAINMODEL_EXPORT=__attribute__\(\(visibility\(\"default\"\)\)\)
    )
endif()

# 链接SDK库
target_link_directories(TerrainModel PRIVATE ${SDK_LIB_DIR})
target_link_libraries(TerrainModel
    PRIVATE
    # SDK库
    ${SDK_LIB_DIR}/TerrainModelCore.lib
    ${SDK_LIB_DIR}/TerrainModelFormats.lib
    ${SDK_LIB_DIR}/Bentley.lib
    ${SDK_LIB_DIR}/ECObjects.lib
    ${SDK_LIB_DIR}/BentleyGeom.lib
    ${SDK_LIB_DIR}/iModelPlatform.lib
    ${SDK_LIB_DIR}/BentleyGeomSerialization.lib
)

# 安装目标
install(TARGETS TerrainModel
    EXPORT TerrainModelTargets
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
    INCLUDES DESTINATION include
)

# 安装头文件
install(DIRECTORY include/
    DESTINATION include
    FILES_MATCHING PATTERN "*.h"
)

# 复制构建的DLL到bin目录以便测试访问
# 注释掉暂时未实现的代码，等SDK库问题解决后再启用
# add_custom_command(TARGET TerrainModel POST_BUILD
#     COMMAND ${CMAKE_COMMAND} -E copy
#     $<TARGET_FILE:TerrainModel>
#     ${CMAKE_BINARY_DIR}/bin/$<TARGET_FILE_NAME:TerrainModel>
# )

# 设置属性
set_target_properties(TerrainModel PROPERTIES
    WINDOWS_EXPORT_ALL_SYMBOLS ON
)

# 启用测试
enable_testing() 