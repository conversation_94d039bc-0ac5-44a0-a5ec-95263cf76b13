cmake_minimum_required(VERSION 3.14)

project(Domain
    VERSION 1.0.0
    LANGUAGES CXX
    DESCRIPTION "RealityCore Domain Libraries"
)

# 设置C++标准
set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# SDK路径检查与传递
if(NOT DEFINED SDK_ROOT)
    message(WARNING "SDK_ROOT not defined in parent scope, TerrainModel may not find required libraries")
else()
    # 传递SDK路径到子项目
    set(SDK_INCLUDE_DIR "${SDK_ROOT}/include")
    set(SDK_LIB_DIR "${SDK_ROOT}/lib")
endif()

# 设置输出目录
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# 编译选项
if(MSVC)
    add_compile_options(
        /W4     # 警告级别
        /wd4251 # 类需要dll导出接口警告禁用
        /wd4275 # dll导出类非dll导出类的派生警告禁用
        /MP     # 多处理器编译
    )
else()
    add_compile_options(
        -Wall
        -Wextra
    )
endif()

# 包含子目录
add_subdirectory(TerrainModel) 