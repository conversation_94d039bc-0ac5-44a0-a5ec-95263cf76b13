cmake_minimum_required(VERSION 3.14)

project(RealityCoreAdon
    VERSION 1.0.0
    LANGUAGES CXX
    DESCRIPTION "Reality Core Adon with TerrainModel support"
)

# 设置C++标准
set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# SDK路径设置
set(SDK_ROOT "D:/sdk/native")
set(SDK_INCLUDE_DIR "${SDK_ROOT}/include")
set(SDK_LIB_DIR "${SDK_ROOT}/lib")

# 添加SDK包含目录和库目录
include_directories(${SDK_INCLUDE_DIR})
link_directories(${SDK_LIB_DIR})

# 编译选项
if(MSVC)
    # 禁用警告
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /wd4251 /wd4275")
    # 启用/MP多处理器编译
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /MP")
    # 启用/Zm150增加预编译头部内存分配限制
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /Zm150")
    # 添加其他MSVC特定定义
    add_definitions(
        -D_CONVERSION_DONT_USE_THREAD_LOCALE
        -D_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES=1
        -D_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES_COUNT=1
        -D_SECURE_SCL_THROWS=1
        -D_SECURE_SCL=0
        -DWIN32_LEAN_AND_MEAN
        -DWINVER=0x0501
        -D_WIN32_WINNT=0x0501
        -D_WIN32_IE=0x0501
        -D_WINDOWS
        -D_USRDLL
        -DWIN32=1
        -DDLL_EXPORT=1
        -DBE_USE_DYNAMIC_PRECOMPILED_HEADER=1
        -DBENTLEY_WIN32
    )
endif()

# 创建必要的目录结构
file(MAKE_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# 设置输出目录
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# 添加子项目
add_subdirectory(Domain)

# 复制BRepCoreCodeStyle.md到输出目录，用于参考
configure_file(${CMAKE_CURRENT_SOURCE_DIR}/BRepCoreCodeStyle.md
               ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/BRepCoreCodeStyle.md
               COPYONLY) 