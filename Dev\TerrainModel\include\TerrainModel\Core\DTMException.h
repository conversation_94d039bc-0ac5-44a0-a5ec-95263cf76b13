/*---------------------------------------------------------------------------------------------
 * Copyright (c) Bentley Systems, Incorporated. All rights reserved.
 * See LICENSE.md in the repository root for full copyright notice.
 *--------------------------------------------------------------------------------------------*/

#pragma once

#include "TerrainModel/TerrainModel.h"
#include <stdexcept>
#include <string>

BEGIN_BENTLEY_TERRAINMODEL_NAMESPACE

/*=======================================================================================*//**
 * @brief DTM基础异常类
 * @details 所有DTM相关异常的基类
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
class TERRAINMODEL_EXPORT DTMException : public std::runtime_error
{
public:
    explicit DTMException(const std::string& message) 
        : std::runtime_error(message) {}
    
    explicit DTMException(const char* message) 
        : std::runtime_error(message) {}
    
    virtual ~DTMException() noexcept = default;
};

/*=======================================================================================*//**
 * @brief DTM文件操作异常
 * @details 文件读写相关的异常
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
class TERRAINMODEL_EXPORT DTMFileException : public DTMException
{
public:
    explicit DTMFileException(const std::string& message) 
        : DTMException(message) {}
    
    explicit DTMFileException(const char* message) 
        : DTMException(message) {}
};

/*=======================================================================================*//**
 * @brief DTM无效操作异常
 * @details 当执行无效操作时抛出的异常
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
class TERRAINMODEL_EXPORT DTMInvalidOperationException : public DTMException
{
public:
    explicit DTMInvalidOperationException(const std::string& message) 
        : DTMException(message) {}
    
    explicit DTMInvalidOperationException(const char* message) 
        : DTMException(message) {}
};

/*=======================================================================================*//**
 * @brief DTM参数异常
 * @details 当传入无效参数时抛出的异常
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
class TERRAINMODEL_EXPORT DTMArgumentException : public DTMException
{
public:
    explicit DTMArgumentException(const std::string& message) 
        : DTMException(message) {}
    
    explicit DTMArgumentException(const char* message) 
        : DTMException(message) {}
};

/*=======================================================================================*//**
 * @brief DTM内存异常
 * @details 内存分配失败时抛出的异常
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
class TERRAINMODEL_EXPORT DTMMemoryException : public DTMException
{
public:
    explicit DTMMemoryException(const std::string& message) 
        : DTMException(message) {}
    
    explicit DTMMemoryException(const char* message) 
        : DTMException(message) {}
};

/*=======================================================================================*//**
 * @brief DTM重复点异常
 * @details 当检测到重复点时抛出的异常
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
class TERRAINMODEL_EXPORT DTMDuplicatePointException : public DTMException
{
private:
    DPoint3d m_point;
    DTMFeatureType m_featureType;
    DTMFeatureId m_featureId;
    DTMUserTag m_userTag;

public:
    DTMDuplicatePointException(const DPoint3d& point, DTMFeatureType featureType, 
                              DTMFeatureId featureId, DTMUserTag userTag);
    
    const DPoint3d& GetPoint() const { return m_point; }
    DTMFeatureType GetFeatureType() const { return m_featureType; }
    DTMFeatureId GetFeatureId() const { return m_featureId; }
    DTMUserTag GetUserTag() const { return m_userTag; }
};

/*=======================================================================================*//**
 * @brief DTM特征交叉异常
 * @details 当特征线交叉时抛出的异常
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
class TERRAINMODEL_EXPORT DTMCrossingFeatureException : public DTMException
{
private:
    DPoint2d m_intersectionPoint;
    DTMFeatureType m_featureType1;
    DTMFeatureId m_featureId1;
    int64_t m_segmentOffset1;
    double m_elevation1;
    double m_distance1;
    DTMFeatureType m_featureType2;
    DTMFeatureId m_featureId2;
    int64_t m_segmentOffset2;
    double m_elevation2;
    double m_distance2;

public:
    DTMCrossingFeatureException(const DPoint2d& intersectionPoint,
                               DTMFeatureType featureType1, DTMFeatureId featureId1, 
                               int64_t segmentOffset1, double elevation1, double distance1,
                               DTMFeatureType featureType2, DTMFeatureId featureId2, 
                               int64_t segmentOffset2, double elevation2, double distance2);

    const DPoint2d& GetIntersectionPoint() const { return m_intersectionPoint; }
    DTMFeatureType GetFeatureType1() const { return m_featureType1; }
    DTMFeatureId GetFeatureId1() const { return m_featureId1; }
    int64_t GetSegmentOffset1() const { return m_segmentOffset1; }
    double GetElevation1() const { return m_elevation1; }
    double GetDistance1() const { return m_distance1; }
    DTMFeatureType GetFeatureType2() const { return m_featureType2; }
    DTMFeatureId GetFeatureId2() const { return m_featureId2; }
    int64_t GetSegmentOffset2() const { return m_segmentOffset2; }
    double GetElevation2() const { return m_elevation2; }
    double GetDistance2() const { return m_distance2; }
};

/*=======================================================================================*//**
 * @brief DTM三角化异常
 * @details 三角化过程中出现错误时抛出的异常
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
class TERRAINMODEL_EXPORT DTMTriangulationException : public DTMException
{
private:
    int m_errorCode;
    std::string m_detailedMessage;

public:
    DTMTriangulationException(int errorCode, const std::string& message, 
                             const std::string& detailedMessage = "");
    
    int GetErrorCode() const { return m_errorCode; }
    const std::string& GetDetailedMessage() const { return m_detailedMessage; }
};

/*=======================================================================================*//**
 * @brief DTM数据验证异常
 * @details 数据验证失败时抛出的异常
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
class TERRAINMODEL_EXPORT DTMValidationException : public DTMException
{
private:
    std::vector<std::string> m_validationErrors;

public:
    explicit DTMValidationException(const std::string& message);
    DTMValidationException(const std::string& message, 
                          const std::vector<std::string>& validationErrors);
    
    const std::vector<std::string>& GetValidationErrors() const { return m_validationErrors; }
    void AddValidationError(const std::string& error) { m_validationErrors.push_back(error); }
};

/*=======================================================================================*//**
 * @brief 异常工厂类
 * @details 提供创建各种DTM异常的便捷方法
 * @bsiclass
+===============+===============+===============+===============+===============+======*/
class TERRAINMODEL_EXPORT DTMExceptionFactory
{
public:
    static DTMFileException CreateFileNotFoundException(const std::string& filename);
    static DTMFileException CreateFileAccessException(const std::string& filename);
    static DTMArgumentException CreateNullArgumentException(const std::string& paramName);
    static DTMArgumentException CreateInvalidArgumentException(const std::string& paramName, 
                                                              const std::string& reason);
    static DTMInvalidOperationException CreateInvalidStateException(const std::string& operation);
    static DTMMemoryException CreateOutOfMemoryException(size_t requestedSize);
    static DTMTriangulationException CreateTriangulationFailedException(int errorCode);
    static DTMValidationException CreateDataValidationException(
        const std::vector<std::string>& errors);
};

END_BENTLEY_TERRAINMODEL_NAMESPACE
