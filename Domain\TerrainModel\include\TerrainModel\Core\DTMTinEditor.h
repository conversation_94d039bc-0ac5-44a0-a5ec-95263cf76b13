/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See LICENSE.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModel/TerrainModel.h"
#include "TerrainModel/Core/DTM.h"
#include "TerrainModel/Core/DTMFeature.h"
#include <vector>
#include <tuple>

BEGIN_BENTLEY_TERRAINMODEL_NAMESPACE

// 前向声明
class DTMTinEditorImpl;

/*=================================================================================**//**
* @brief 地形模型编辑器类
* @details 用于编辑三角不规则网络(TIN)地形模型
* @bsiclass
+===============+===============+===============+===============+===============+======*/
class TERRAINMODEL_EXPORT DTMTinEditor
{
private:
    //! PIMPL实现
    std::unique_ptr<DTMTinEditorImpl> m_impl;

public:
    //! 默认构造函数
    DTMTinEditor();
    
    //! 基于DTM构造函数
    //! @param[in] dtm 地形模型
    explicit DTMTinEditor(const DTM& dtm);
    
    //! 析构函数
    ~DTMTinEditor();
    
    //! 拷贝构造函数
    DTMTinEditor(const DTMTinEditor& other);
    
    //! 移动构造函数
    DTMTinEditor(DTMTinEditor&& other) noexcept;
    
    //! 拷贝赋值运算符
    DTMTinEditor& operator=(const DTMTinEditor& other);
    
    //! 移动赋值运算符
    DTMTinEditor& operator=(DTMTinEditor&& other) noexcept;

    //=======================================================================================
    //! @name 编辑操作
    //! @{
    
    //! 选择特征进行编辑
    //! @param[in] featureType 特征类型
    //! @param[in] x X坐标
    //! @param[in] y Y坐标
    //! @param[in] z Z坐标
    //! @return 是否找到特征
    bool Select(DTMFeatureType featureType, double x, double y, double z);
    
    //! 删除所选特征
    //! @return 删除是否成功
    bool Delete();
    
    //! 获取所选特征的点
    //! @param[out] points 点坐标集合
    //! @return 是否成功获取点
    bool GetSelectedFeaturePoints(std::vector<std::tuple<double, double, double>>& points) const;
    
    //! @}
};

END_BENTLEY_TERRAINMODEL_NAMESPACE
