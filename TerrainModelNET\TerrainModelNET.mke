#---------------------------------------------------------------------------------------------
#   Copyright (c) Bentley Systems, Incorporated. All rights reserved.
#   See COPYRIGHT.md in the repository root for full copyright notice.
#---------------------------------------------------------------------------------------------
#----------------------------------------------------------------------------------------------------------------------------------------------------
# 
#----------------------------------------------------------------------------------------------------------------------------------------------------
baseDir          = $(_MakeFilePath)
PolicyFile       = $(SrcRoot)imodel02/RealityCore/TerrainModel/privmki/AssertTerrainModelPolicy.mki
SolutionPolicyMki=$(baseDir)TerrainModelNET.mki

%include  mdl.mki


%ifdef CREATE_STATIC_LIBRARIES
%error can't be build as a static library
%endif

#GeneratedCppDir         = $(baseDir)GeneratedCpp/
#GeneratedApiDir         = $(baseDir)GeneratedApi/
#ManagedCommonDir        = $(baseDir)Common/

#----------------------------------------------------------------------
#       Define outputs
#----------------------------------------------------------------------
o = $(BentleyTerrainModelNetObj)

appName               = Bentley.TerrainModelNET

#----------------------------------------------------------------------------------------------------------------------------------------------------
# 
#----------------------------------------------------------------------------------------------------------------------------------------------------
always:
    ~mkdir $(o)

#----------------------------------------------------------------------------------------------------------------------------------------------------
# 
#----------------------------------------------------------------------------------------------------------------------------------------------------
%include compileForCLRStart.mki

#....................................................................................................................................................
CCompDebugOptions = $(CCompDebugOffSwitch)

#$(o)FxCopSuppressions$(oext) : $(baseDir)FxCopSuppressions.cpp

CCompDebugOptions =% $[CCompDebugDefault]

CCompOpts + -AI$(ContextSubPartsAssembliesDir)

#....................................................................................................................................................
PchCompiland        = $(baseDir)StdAfx.cpp
PchOutputDir        = $(o)
PchExtraOptions    = -Zm150

#PchArgumentsDepends     = $(MultiCompileDepends)

PchCompileForClr        = 1
PchExtraOptions         + -AI$(ContextSubPartsAssembliesDir)

%include $(SharedMki)PreCompileHeader.mki

CCPchOpts = $(UsePrecompiledHeaderOptions)
CPchOpts  = $(UsePrecompiledHeaderOptions)
PCHHeaderDependency     = $(o)$(PCHCompilandBaseName).pch
PCHHeaderObject         = $(o)$(PCHCompilandBaseName)$(oext)

MultiCompileDepends     = $(_MakeFileSpec)

dirToSearch=$(baseDir)
%include cincapnd.mki

DependsOnHeaders = \
$(DependsOnAssemblies) \
$(basedir)Bentley.Civil.DTM.h \
$(basedir)DTM.h \
$(basedir)DTMFeatureEnumerator.h \
$(basedir)DTMMeshEnumerator.h \
$(basedir)DTMHelpers.h \
$(basedir)DTMMesh.h \
$(basedir)DTMDrapedLinearElement.h \
$(basedir)DTMSideSlopeInput.h \
$(basedir)DTMSideSlopeInputPoint.h \
$(basedir)DTMException.h \
$(basedir)DTMTinEditor.h \
$(basedir)Stdafx.h \
$(PCHHeaderObject)

#$(basedir)DTMEdges.h \
#$(basedir)DTMFeature.h \
#....................................................................................................................................................
%include MultiCppCompileRule.mki

$(o)DTM$(oext)      : $(basedir)DTM.cpp $(DependsOnHeaders) ${MultiCompileDepends}

$(o)DTMFeature$(oext)       : $(basedir)DTMFeature.cpp $(DependsOnHeaders) ${MultiCompileDepends}

$(o)DTMFeatureEnumerator$(oext)     : $(basedir)DTMFeatureEnumerator.cpp $(DependsOnHeaders) ${MultiCompileDepends}

$(o)DTMMeshEnumerator$(oext)     : $(basedir)DTMMeshEnumerator.cpp $(DependsOnHeaders) ${MultiCompileDepends}

$(o)DTMHelpers$(oext)       : $(basedir)DTMHelpers.cpp $(DependsOnHeaders) ${MultiCompileDepends}

$(o)DTMMesh$(oext)     : $(basedir)DTMMesh.cpp $(DependsOnHeaders) ${MultiCompileDepends}

$(o)DTMTinEditor$(oext)     : $(basedir)DTMTinEditor.cpp $(DependsOnHeaders) ${MultiCompileDepends}

$(o)DTMDrapedLinearElement$(oext)       : $(basedir)DTMDrapedLinearElement.cpp $(DependsOnHeaders) ${MultiCompileDepends}

$(o)DTMSideSlopeInput$(oext)        : $(basedir)DTMSideSlopeInput.cpp $(DependsOnHeaders) ${MultiCompileDepends}

$(o)DTMSideSlopeInputPoint$(oext)       : $(basedir)DTMSideSlopeInputPoint.cpp $(DependsOnHeaders) ${MultiCompileDepends}

$(o)DTMPond$(oext)      : $(basedir)DTMPond.cpp $(DependsOnHeaders) ${MultiCompileDepends}

$(o)WaterAnalysis$(oext)      : $(basedir)WaterAnalysis.cpp $(DependsOnHeaders) ${MultiCompileDepends}

%include MultiCppCompileGo.mki

#....................................................................................................................................................
# Auto-generated AssemblyInfo.cpp file (spawned by compileForCLRStop.mki) should not include the PCH header file.
CCPchOpts =

%include compileForCLRStop.mki

CCPchOpts = 
CPchOpts  = 
$(o)Caching$(oext)      : $(basedir)Caching.cpp $(DependsOnHeaders) ${MultiCompileDepends}

#----------------------------------------------------------------------------------------------------------------------------------------------------
# 
#----------------------------------------------------------------------------------------------------------------------------------------------------
DLM_NAME          = $(appName)
DLM_DEST          = $(o)
DLM_OBJECT_DEST   = $(o)
DLM_OBJECT_FILES  = $(MultiCompileObjectList) $(PCHHeaderObject) $(o)Caching$(oext)
DLM_NOINITFUNC    = 1
DLM_NOENTRY       = 1
DLM_NOMSBUILTINS  = 1
DLM_NO_DEF        = 1
DLM_NO_DLS        = 1

LINKER_LIBRARIES  = $(ContextSubpartsLibs)BentleyGeom.lib
LINKER_LIBRARIES  + $(ContextSubpartsLibs)TerrainModelCore.lib

ASSEMBLY_NO_AUTHENTICODE_SIGN = 1
%include $(SharedMki)VersionedPartSignatureDefaults.mki
ASSEMBLY_KEYFILE        =% $(ASSEMBLY_KEYFILE_DEFAULT)
ASSEMBLY_TESTKEYFILE       =% $(ASSEMBLY_TESTKEYFILE_DEFAULT)

ASSEMBLY_NAME               = $(DLM_NAME)
ASSEMBLY_STRONGNAME         = 1
ASSEMBLY_TITLE              = $(DLM_NAME)
ASSEMBLY_PRODUCT_NAME       = Bentley TerrainModel Core NET
ASSEMBLY_DESCRIPTION        = Managed API for Bentley TerrainModel
ASSEMBLY_VERSION            = *******
ASSEMBLY_FILE_VERSION       = *******
ASSEMBLY_COMPANY_NAME       = $(companyName)
ASSEMBLY_COPYRIGHT          = $(Bentley_Copyright)

ASSEMBLY_COMVISIBLE         = false
ASSEMBLY_CLSCOMPLIANT       = true

# ======================================================
# undef DLM_CREATE_LIB_CONTEXT_LINK since there is no 
# .lib file created that needs to be linked to the build 
# context directory
# ======================================================
%undef DLM_CREATE_LIB_CONTEXT_LINK
#FxCopCustomDictionaryFile=$(baseDir)fxcopDictionary.xml
#SuppressCodeAnalysis=1
%include linkMixedAssembly.mki

