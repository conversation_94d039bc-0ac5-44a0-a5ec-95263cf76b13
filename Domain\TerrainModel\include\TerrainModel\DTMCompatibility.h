/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See LICENSE.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModel/TerrainModel.h"

/**
 * @file DTMCompatibility.h
 * @brief 提供与原始TerrainModelNET代码的兼容性
 * 
 * 该文件包含类型定义和兼容性宏，以允许现有代码使用新的结构。
 * 在迁移过程中使用，最终应删除对此文件的依赖。
 */

// 兼容原始命名空间
#ifndef BEGIN_BENTLEY_TERRAINMODELNET_NAMESPACE
#define BEGIN_BENTLEY_TERRAINMODELNET_NAMESPACE namespace Bentley { namespace TerrainModelNET {
#define END_BENTLEY_TERRAINMODELNET_NAMESPACE }}
#endif

// 兼容类型映射
BEGIN_BENTLEY_TERRAINMODELNET_NAMESPACE

// 将旧命名空间的类型映射到新命名空间
using DTM = Bentley::TerrainModel::DTM;
using DTMFeature = Bentley::TerrainModel::DTMFeature;
using DTMPointFeature = Bentley::TerrainModel::DTMPointFeature;
using DTMLinearFeature = Bentley::TerrainModel::DTMLinearFeature;
using DTMMesh = Bentley::TerrainModel::DTMMesh;
using DTMFeatureEnumerator = Bentley::TerrainModel::DTMFeatureEnumerator;
using DTMMeshEnumerator = Bentley::TerrainModel::DTMMeshEnumerator;
using DTMSideSlopeInput = Bentley::TerrainModel::DTMSideSlopeInput;
using DTMSideSlopeInputPoint = Bentley::TerrainModel::DTMSideSlopeInputPoint;
using DTMPond = Bentley::TerrainModel::DTMPond;
using DTMTinEditor = Bentley::TerrainModel::DTMTinEditor;
using DTMFeatureCache = Bentley::TerrainModel::DTMFeatureCache;
using DTMCachedFeature = Bentley::TerrainModel::DTMCachedFeature;
using DTMHelpers = Bentley::TerrainModel::DTMHelpers;
using DTMException = Bentley::TerrainModel::DTMException;
using DTMDrapedLinearElement = Bentley::TerrainModel::DTMDrapedLinearElement;
using DTMUserTagRange = Bentley::TerrainModel::DTMUserTagRange;

// 枚举类型映射
using DTMFeatureType = Bentley::TerrainModel::DTMFeatureType;
using DTMDynamicFeatureType = Bentley::TerrainModel::DTMDynamicFeatureType;
using SlopeType = Bentley::TerrainModel::SlopeType;

// 基本类型定义
using DTMFeatureId = Bentley::TerrainModel::DTMFeatureId;
using DTMUserTag = Bentley::TerrainModel::DTMUserTag;
using DTMElevation = Bentley::TerrainModel::DTMElevation;
using BentleyStatus = Bentley::TerrainModel::BentleyStatus;

// 兼容性常量
#define SUCCESS Bentley::TerrainModel::SUCCESS
#define ERROR Bentley::TerrainModel::ERROR

END_BENTLEY_TERRAINMODELNET_NAMESPACE

// 添加旧头文件的包含映射，允许使用#include "OldHeader.h"语法
#ifdef TERRAINMODEL_COMPATIBILITY_INCLUDES
// 示例: #define Bentley.Civil.DTM.h <TerrainModel/DTMCompatibility.h>
#endif 