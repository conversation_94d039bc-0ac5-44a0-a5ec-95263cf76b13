/*---------------------------------------------------------------------------------------------
* Copyright (c) Bentley Systems, Incorporated. All rights reserved.
* See LICENSE.md in the repository root for full copyright notice.
*--------------------------------------------------------------------------------------------*/
#pragma once

#include "TerrainModel/TerrainModel.h"
#include "TerrainModel/Core/DTMMesh.h"
#include <vector>
#include <memory>
#include <iterator>

BEGIN_BENTLEY_TERRAINMODEL_NAMESPACE

// 前向声明
class DTM;
class DTMMeshEnumeratorImpl;

/*=================================================================================**//**
* @brief 地形网格枚举器
* @details 用于遍历DTM中的网格数据
* @bsiclass
+===============+===============+===============+===============+===============+======*/
class TERRAINMODEL_EXPORT DTMMeshEnumerator
{
private:
    std::unique_ptr<DTMMeshEnumeratorImpl> m_impl;
    
public:
    //! 构造函数
    //! @param[in] dtm 地形模型
    DTMMeshEnumerator(const DTM& dtm);
    
    //! 析构函数
    ~DTMMeshEnumerator();
    
    //! 创建网格枚举器
    //! @param[in] dtm 地形模型
    //! @return 网格枚举器
    static std::unique_ptr<DTMMeshEnumerator> Create(const DTM& dtm);
    
    //! 设置是否使用多面体头
    //! @param[in] use 是否使用多面体头
    void SetUsePolyfaceHeader(bool use);
    
    //! 设置最大三角形数量
    //! @param[in] maxTriangles 最大三角形数量
    void SetMaxTriangles(int maxTriangles);
    
    //=======================================================================================
    //! @name 迭代器支持
    //! @{
    
    //! 迭代器类型定义
    class Iterator 
    {
    private:
        DTMMeshEnumerator* m_owner;
        int m_position;
        
    public:
        using iterator_category = std::forward_iterator_tag;
        using value_type = DTMMesh;
        using difference_type = std::ptrdiff_t;
        using pointer = DTMMesh*;
        using reference = DTMMesh&;
        
        //! 构造函数
        //! @param[in] owner 枚举器
        //! @param[in] position 位置
        Iterator(DTMMeshEnumerator* owner, int position);
        
        //! 解引用操作符
        DTMMesh operator*() const;
        
        //! 前置递增操作符
        Iterator& operator++();
        
        //! 后置递增操作符
        Iterator operator++(int);
        
        //! 相等比较操作符
        bool operator==(const Iterator& other) const;
        
        //! 不等比较操作符
        bool operator!=(const Iterator& other) const;
    };
    
    //! 获取起始迭代器
    //! @return 起始迭代器
    Iterator begin();
    
    //! 获取结束迭代器
    //! @return 结束迭代器
    Iterator end();
    
    //! @}
    
    //! 获取网格总数
    //! @return 网格总数
    int GetCount() const;
    
    //! 获取指定位置的网格
    //! @param[in] index 索引
    //! @return 网格
    DTMMesh GetMeshAt(int index) const;
};

END_BENTLEY_TERRAINMODEL_NAMESPACE
