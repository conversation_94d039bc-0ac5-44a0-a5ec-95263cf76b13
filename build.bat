@echo off
setlocal enabledelayedexpansion

:: 脚本路径和项目根目录
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%"
cd "%PROJECT_ROOT%"

:: 检查build目录是否存在
if not exist build (
    echo 构建目录不存在，请先运行 configure.bat
    exit /b 1
)

:: 设置构建参数
set "BUILD_TYPE=Release"
set "CONFIG_OPTION=--config %BUILD_TYPE%"

:: 允许自定义构建类型
if not "%1"=="" (
    set "BUILD_TYPE=%1"
    set "CONFIG_OPTION=--config %BUILD_TYPE%"
)

:: 进入构建目录
cd build

:: 构建项目
echo 正在构建项目 (配置: %BUILD_TYPE%)...
cmake --build . %CONFIG_OPTION% --parallel %NUMBER_OF_PROCESSORS%

if %ERRORLEVEL% neq 0 (
    echo 构建失败，错误代码: %ERRORLEVEL%
    exit /b %ERRORLEVEL%
) else (
    echo 构建成功!
    echo 输出文件位于: %PROJECT_ROOT%build\bin\%BUILD_TYPE%
)

:: 返回项目根目录
cd "%PROJECT_ROOT%" 